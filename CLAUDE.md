# 开发指南

## 理念

### 核心信念

- **增量进步而非大爆炸** - 小改动，能编译，通过测试
- **从现有代码中学习** - 实施前先研究和规划
- **务实而非教条** - 适应项目实际情况
- **清晰的意图而非花哨的代码** - 平凡而明显

### 简单意味着

- 每个函数/类单一职责
- 避免过早抽象
- 不要花哨的技巧 - 选择平凡的解决方案
- 如果需要解释，那就太复杂了

## 流程

### 1. 规划与分阶段

将复杂工作分解为 3-5 个阶段。在 `IMPLEMENTATION_PLAN.md` 中记录：

```markdown
## 第 N 阶段: [名称]
**目标**: [具体交付物]
**成功标准**: [可测试的结果]
**测试**: [具体测试用例]
**状态**: [未开始|进行中|已完成]
```
- 推进时更新状态
- 所有阶段完成后删除文件

### 2. 实施流程

1. **理解** - 研究代码库中的现有模式
2. **测试** - 先写测试 (红色)
3. **实施** - 最少代码通过测试 (绿色)
4. **重构** - 在测试通过的情况下清理
5. **提交** - 带有清晰的消息链接到计划

### 3. 遇到困难时 (3 次尝试后)

**关键**: 每个问题最多尝试 3 次，然后停止。

1. **记录失败原因**:
   - 尝试了什么
   - 具体的错误信息
   - 为什么认为失败了

2. **研究替代方案**:
   - 找到 2-3 个类似的实现
   - 注意使用的不同方法

3. **质疑基础**:
   - 这是正确的抽象级别吗？
   - 可以分解为更小的问题吗？
   - 有完全更简单的方法吗？

4. **尝试不同角度**:
   - 不同的库/框架功能？
   - 不同的架构模式？
   - 移除抽象而不是添加？

## 技术标准

### 架构原则

- **组合优于继承** - 使用依赖注入
- **接口优于单例** - 启用测试和灵活性
- **显式优于隐式** - 清晰的数据流和依赖
- **尽可能测试驱动** - 永不禁用测试，修复它们

### 代码质量

- **每次提交必须**:
  - 编译成功
  - 通过所有现有测试
  - 包含新功能的测试
  - 遵循项目格式化/代码检查

- **提交前**:
  - 运行格式化器/代码检查器
  - 自我审查更改
  - 确保提交消息解释"为什么"

### 错误处理

- 快速失败并提供描述性消息
- 包含调试上下文
- 在适当级别处理错误
- 永不默默吞下异常

## 决策框架

当存在多个有效方法时，基于以下选择：

1. **可测试性** - 我能轻松测试这个吗？
2. **可读性** - 6 个月后有人能理解这个吗？
3. **一致性** - 这符合项目模式吗？
4. **简单性** - 这是最简单的可行解决方案吗？
5. **可逆性** - 以后更改有多难？

## 项目集成

### 学习代码库

- 找到 3 个相似的功能/组件
- 识别常见的模式和约定
- 尽可能使用相同的库/工具
- 遵循现有的测试模式

### 工具

- 使用项目现有的构建系统
- 使用项目的测试框架
- 使用项目的格式化/代码检查器设置
- 不要在没有充分理由的情况下引入新工具

## 质量标准

### 完成定义

- [ ] 测试已编写并通过
- [ ] 代码遵循项目约定
- [ ] 没有代码检查器/格式化器警告
- [ ] 提交消息清晰
- [ ] 实现符合计划
- [ ] 没有 TODO 没有编号

### 测试指南

- 测试行为而非实现
- 尽可能每个测试一个断言
- 清晰的测试名称描述场景
- 使用现有的测试工具/助手
- 测试应该是确定性的

## 重要提醒

**永远不要**:
- 使用 `--no-verify` 绕过提交钩子
- 禁用测试而不是修复它们
- 提交无法编译的代码
- 做假设 - 用现有代码验证

**总是**:
- 增量提交工作代码
- 推进时更新计划文档
- 从现有实现中学习
- 3 次失败尝试后停止并重新评估