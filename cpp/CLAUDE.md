# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是一个名为 "zexuan" 的 C++23 项目，专注于工业通信协议和网络处理。采用事件驱动架构，支持 TCP、UDP 和 Unix 域套接字。

## 构建系统

### CMake 配置
- **最低版本**: 3.14-3.22
- **C++ 标准**: C++23
- **编译器**: Clang (显式配置)
- **包管理器**: CPM (CMake 包管理器)

### 常用开发命令

```bash
# 配置构建 (调试模式)
cmake -B build -S . -DCMAKE_BUILD_TYPE=Debug -DBUILD_TESTS=ON

# 构建项目
cmake --build build

# 运行测试
ctest --test-dir build

# 使用 sanitizer 构建
cmake -B build -S . -DCMAKE_BUILD_TYPE=Debug -DUSE_SANITIZER="Address;Undefined;Leak"

# 生成 compile_commands.json (默认启用)
# 使用: ln -s build/compile_commands.json .
```

### 测试
- 传递 `-DBUILD_TESTS=ON` 给 CMake 时构建测试
- 测试可执行文件位于 `build/test/`
- 使用 `ctest --test-dir build --verbose` 查看详细输出

### 代码质量工具
- **格式化**: `.clang-format` (Google 风格，100列限制)
- **静态分析**: clang-tidy, include-what-you-use, cppcheck (通过 cmake/tools.cmake)
- **Sanitizers**: 地址、未定义行为、内存泄漏检测可用

## 架构概述

### 核心库
项目构建 5 个共享库：
1. **zexuan_base** (`source/zexuan/base/`) - 基础类和工具
2. **zexuan_utils** (`source/zexuan/utils/`) - 实用函数和助手
3. **zexuan_event** (`source/zexuan/event/`) - 事件循环和网络层
4. **zexuan_protocol** (`source/zexuan/protocol/`) - 协议处理
5. **zexuan_bus** (`source/zexuan/bus/`) - 消息总线和通信

### 关键组件

#### 事件系统 (`source/zexuan/event/`)
- **网络**: TCP、UDP、UDS 支持在 `net/` 子目录
- **系统**: 定时器、信号、inotify 处理器在 `sys/` 子目录
- 异步 I/O 的事件驱动架构

#### 协议处理 (`source/zexuan/protocol/`)
- **protocol_server/**: 服务器端协议处理
  - `transform/gw104/`: GW104 协议转换
  - `server/`: 协议服务器实现
  - `service/`: 协议服务
  - `gateway/`: 协议网关
- **protocol_client/**: 客户端协议处理

#### 应用程序 (`app/`)
- `protocol_server/` - 主协议服务器应用
- `protocol_client/` - 协议客户端应用
- `tcp_bus_server/` - TCP 消息总线服务器
- `devices/` - 设备专用应用 (rename_client, tcp_bus_client)

## 代码约定

### 文件结构
- 源文件在 `source/` 目录，扩展名 `.cpp`
- 头文件在 `include/` 目录，扩展名 `.hpp`
- 公共头文件镜像源代码结构
- 测试在 `test/` 目录
- 示例在 `examples/` 目录

### 依赖项
- **nlohmann_json** (v3.12.0) - JSON 处理
- **spdlog** (v1.15.3) - 日志框架
- 依赖项通过 CPM 在 `CMakeLists.txt` 中管理

### 配置
- 主配置在 `config/config.json`
- 运行时可配置设置
- 与 spdlog 的日志集成

## 开发指南

### 代码风格
- 遵循 Google C++ 风格指南 (`.clang-format`)
- 100 列限制
- 适当使用 C++23 特性
- 优先使用组合而非继承
- 使用依赖注入以获得更好的可测试性

### 错误处理
- 快速失败并提供描述性消息
- 包含调试上下文
- 在适当级别处理错误
- 对异常情况使用异常

### 测试
- 为新功能编写测试
- 使用 `test/` 目录中的现有测试模式
- 测试行为而非实现
- 测试应该是确定性的

### 内存管理
- 使用智能指针 (std::unique_ptr, std::shared_ptr)
- 尽可能避免原始指针
- 遵循 RAII 原则
- 在事件驱动代码中注意对象生命周期

## 常见模式

### 事件循环集成
- 大多数组件与事件循环系统集成
- 对网络操作使用异步模式
- 通过回调或方法重写处理事件

### 协议处理
- 多层协议栈
- 协议转换的转换层
- 业务逻辑的服务层
- 路由的网关层

### 连接管理
- 自动重连逻辑
- 适当的连接池
- 带启动/停止接口的生命周期管理

## 当前开发状态

- **版本**: 1.0.5
- **活跃开发**: 协议客户端改进、生命周期组件
- **重点领域**: 工业协议支持 (GW104)、消息总线优化
- **构建系统**: 现代 CMake 和全面工具

## 重要说明

- 项目使用 ccache 进行编译缓存
- 静态分析工具可用但需要显式启用
- 可以启用 sanitizer 来调试内存问题
- 示例默认构建但可以通过 `-DBUILD_EXAMPLES=OFF` 禁用