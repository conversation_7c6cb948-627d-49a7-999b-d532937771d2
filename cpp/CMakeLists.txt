cmake_minimum_required(VERSION 3.14...3.22)

# 设置编译器为 clang（必须在 project() 之前设置）
set(CMAKE_C_COMPILER /usr/sbin/clang)
set(CMAKE_CXX_COMPILER /usr/sbin/clang++)

project(zexuan VERSION 1.0.5)

# 设置 C++ 标准为 C++23
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 导出编译命令（必须在 add_subdirectory 之前设置）
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# 添加 ccache 支持
find_program(CCACHE_PROGRAM ccache)
if(CCACHE_PROGRAM)
    message(STATUS "Using ccache: ${CCACHE_PROGRAM}")
    set(CMAKE_C_COMPILER_LAUNCHER ${CCACHE_PROGRAM})
    set(CMAKE_CXX_COMPILER_LAUNCHER ${CCACHE_PROGRAM})
else()
    message(WARNING "ccache not found, compilation will not be cached")
endif()

# 设置 CPM 缓存目录
set(CPM_SOURCE_CACHE ${CMAKE_CURRENT_SOURCE_DIR}/.cpm_cache)

include(cmake/CPM.cmake)

#set(USE_STATIC_ANALYZER "clang-tidy;iwyu;cppcheck" CACHE STRING "Enable static analyzers")
set(USE_SANITIZER "Address;Undefined;Leak" CACHE STRING "Enable sanitizers")

# 导入工具（包括 ccache 支持）
include(cmake/tools.cmake)

# 添加第三方依赖
CPMAddPackage(
    NAME nlohmann_json
    GITHUB_REPOSITORY nlohmann/json
    VERSION 3.12.0
)

CPMAddPackage(
    NAME spdlog
    GITHUB_REPOSITORY gabime/spdlog
    VERSION 1.15.3
    OPTIONS "SPDLOG_BUILD_SHARED ON"
)

# 添加子项目
add_subdirectory(source)
add_subdirectory(app)

# 可选：添加示例程序（需要显式启用）
option(BUILD_EXAMPLES "Build examples" ON)
if(BUILD_EXAMPLES)
    add_subdirectory(examples)
endif()

# 可选：添加测试（需要显式启用）
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    add_subdirectory(test)
endif()
