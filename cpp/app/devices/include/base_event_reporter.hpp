/**
 * @file base_event_reporter.hpp
 * @brief 事件上报器基类定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef DEVICES_BASE_EVENT_REPORTER_HPP
#define DEVICES_BASE_EVENT_REPORTER_HPP

#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>

#include "zexuan/base/types/structs.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/event/event_loop.hpp"

namespace devices {

  /**
   * @brief 事件上报器基类
   *
   * 负责定时生成和发送事件到总线，子类可以继承并扩展功能
   */
  class BaseEventReporter {
  public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用
     * @param interval_seconds 上报间隔（秒）
     */
    BaseEventReporter(zexuan::bus::TcpBusClient& client, zexuan::event::EventLoop& event_loop,
                      double interval_seconds = 3.0)
        : client_(client),
          event_loop_(event_loop),
          interval_seconds_(interval_seconds),
          running_(false) {}

    /**
     * @brief 虚析构函数
     */
    virtual ~BaseEventReporter() { stop(); }

    /**
     * @brief 启动事件上报
     */
    virtual void start() {
      if (running_.load()) {
        spdlog::warn("Event reporter is already running");
        return;
      }

      running_.store(true);
      spdlog::info("Event reporter started with interval {} seconds", interval_seconds_);

      // 使用定时器定期生成事件
      event_loop_.runEvery(interval_seconds_, [this]() {
        if (running_.load()) {
          generateEvent();
        }
      });
    }

    /**
     * @brief 停止事件上报
     */
    virtual void stop() {
      if (!running_.load()) {
        return;
      }

      running_.store(false);
      spdlog::info("Event reporter stopped");
    }

    /**
     * @brief 检查是否正在运行
     * @return true表示正在运行，false表示已停止
     */
    bool isRunning() const { return running_.load(); }

    /**
     * @brief 设置上报间隔
     * @param interval_seconds 新的上报间隔（秒）
     */
    void setInterval(double interval_seconds) {
      interval_seconds_ = interval_seconds;
      spdlog::info("Event reporter interval updated to {} seconds", interval_seconds_);
    }

    /**
     * @brief 获取当前上报间隔
     * @return 当前上报间隔（秒）
     */
    double getInterval() const { return interval_seconds_; }

  protected:
    /**
     * @brief 生成并发送事件（纯虚函数，子类必须实现）
     */
    virtual void generateEvent() = 0;

    /**
     * @brief 创建基础事件消息
     * @param event_type 事件类型
     * @param description 事件描述
     * @return 创建的事件消息
     */
    zexuan::base::EventMessage createBaseEventMessage(int event_type,
                                                      const std::string& description) const {
      zexuan::base::EventMessage event_msg;
      event_msg.event_type = event_type;
      event_msg.source_id = client_.getClientId();
      event_msg.description = description;
      return event_msg;
    }

    /**
     * @brief 发送事件消息
     * @param event_msg 要发送的事件消息
     * @return true表示发送成功，false表示发送失败
     */
    bool sendEventMessage(const zexuan::base::EventMessage& event_msg) const {
      if (!client_.isConnected()) {
        spdlog::debug("Client not connected, skipping event generation");
        return false;
      }

      if (client_.sendEventMessage(event_msg)) {
        spdlog::debug("Generated and sent event: type={}, description={}", event_msg.event_type,
                      event_msg.description);
        return true;
      } else {
        spdlog::error("Failed to send event message");
        return false;
      }
    }

    /**
     * @brief 获取客户端引用
     * @return TCP总线客户端引用
     */
    zexuan::bus::TcpBusClient& getClient() { return client_; }

    /**
     * @brief 获取事件循环引用
     * @return 事件循环引用
     */
    zexuan::event::EventLoop& getEventLoop() { return event_loop_; }

  private:
    zexuan::bus::TcpBusClient& client_;     ///< TCP总线客户端引用
    zexuan::event::EventLoop& event_loop_;  ///< 事件循环引用
    double interval_seconds_;               ///< 上报间隔（秒）
    std::atomic<bool> running_;             ///< 运行状态标志
  };

}  // namespace devices

#endif  // DEVICES_BASE_EVENT_REPORTER_HPP
