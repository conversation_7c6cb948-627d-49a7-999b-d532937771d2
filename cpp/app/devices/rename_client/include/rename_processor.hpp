/**
 * @file rename_processor.hpp
 * @brief 重命名消息处理器定义
 * <AUTHOR>
 * @date 2025-08-26
 */

#ifndef RENAME_CLIENT_RENAME_PROCESSOR_HPP
#define RENAME_CLIENT_RENAME_PROCESSOR_HPP

#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>

#include <atomic>
#include <filesystem>
#include <future>
#include <mutex>
#include <thread>
#include <vector>

#include "../../include/base_message_processor.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/thread_pool.hpp"
#include "zexuan/base/types/structs.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/utils/file_utils.hpp"

namespace fs = std::filesystem;

namespace rename_client {

  /**
   * @brief 重命名消息处理器
   *
   * 专门处理文件重命名请求，使用多线程池并行处理文件重命名操作
   */
  class RenameProcessor : public devices::BaseMessageProcessor {
  public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用
     * @param thread_pool_size 线程池大小，默认32
     */
    RenameProcessor(zexuan::bus::TcpBusClient& client, zexuan::event::EventLoop& event_loop,
                    size_t thread_pool_size = 32)
        : BaseMessageProcessor(client),
          event_loop_(event_loop),
          thread_pool_(std::unique_ptr<zexuan::base::ThreadPool>(
              new zexuan::base::ThreadPool(thread_pool_size))),
          processed_files_(0),
          successful_renames_(0),
          failed_renames_(0) {}

    /**
     * @brief 析构函数，等待所有任务完成
     */
    ~RenameProcessor() { waitForAllTasks(); }

    /**
     * @brief 处理重命名消息
     * @param original_message 原始的CommonMessage
     * @param input_msg 解析后的消息
     * @return true表示处理成功，false表示处理失败
     */
    bool processMessage(const zexuan::base::CommonMessage& original_message,
                        const zexuan::base::Message& input_msg) override {
      spdlog::debug("Processing rename message");

      // 从消息中提取目录路径
      std::string directory_path = input_msg.getTextContent();

      if (directory_path.empty()) {
        spdlog::error("No directory path provided in rename message");
        return sendErrorResponse(original_message, "No directory path provided");
      }

      spdlog::info("RenameProcessor processing directory: {}", directory_path);

      // 发送确认响应
      if (!sendAckResponse(original_message, input_msg)) {
        return false;
      }

      // 异步处理目录重命名
      processDirectoryAsync(directory_path, original_message, input_msg);

      return true;
    }

    /**
     * @brief 检查是否可以处理指定类型的消息
     * @param message_type 消息类型（Message的TYP字段，协议层类型）
     * @return true表示可以处理重命名消息（TYP=0x05）
     */
    bool canProcess(uint8_t message_type) const override {
      return message_type == 0x05;  // 处理协议层Type 5重命名消息
    }

    /**
     * @brief 获取处理器名称
     * @return 处理器名称
     */
    std::string getName() const override { return "RenameProcessor"; }

    /**
     * @brief 等待所有任务完成
     */
    void waitForAllTasks() {
      std::lock_guard<std::mutex> lock(futures_mutex_);
      for (auto& future : pending_futures_) {
        try {
          future.get();
        } catch (const std::exception& e) {
          spdlog::error("Task completion error: {}", e.what());
        }
      }
      pending_futures_.clear();
    }

  private:
    /**
     * @brief 发送确认响应
     */
    bool sendAckResponse(const zexuan::base::CommonMessage& original_message,
                         const zexuan::base::Message& input_msg) {
      auto response = createBaseResponse(original_message, false);  // 不是最后一帧

      std::string ack_content = "RENAME_ACK_" + input_msg.getTextContent();
      auto response_msg = createResponse(input_msg, 0x07, ack_content);  // 0x07 = 激活确认

      std::vector<uint8_t> serialized_response;
      size_t size = response_msg.serialize(serialized_response);

      if (size > 0) {
        response.data = serialized_response;
      } else {
        std::string simple_response = "RENAME_ACK_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());
      }

      return sendResponse(response);
    }

    /**
     * @brief 发送错误响应
     */
    bool sendErrorResponse(const zexuan::base::CommonMessage& original_message,
                           const std::string& error_msg) {
      auto response = createBaseResponse(original_message, true);  // 最后一帧

      std::string error_response = "RENAME_ERROR: " + error_msg;
      response.data.assign(error_response.begin(), error_response.end());

      return sendResponse(response);
    }

    /**
     * @brief 异步处理目录重命名
     */
    void processDirectoryAsync(const std::string& directory_path,
                               const zexuan::base::CommonMessage& original_message,
                               const zexuan::base::Message& input_msg) {
      // 在事件循环中异步处理
      event_loop_.runInLoop([this, directory_path, original_message, input_msg]() {
        processDirectoryMultiThreaded(directory_path, original_message, input_msg);
      });
    }

    /**
     * @brief 多线程处理目录中的文件重命名
     */
    void processDirectoryMultiThreaded(const std::string& directory_path,
                                       const zexuan::base::CommonMessage& original_message,
                                       const zexuan::base::Message& input_msg) {
      // 检查目录是否存在
      if (!fs::exists(directory_path) || !fs::is_directory(directory_path)) {
        spdlog::error("Invalid directory path: {}", directory_path);
        sendFinalResponse(original_message, input_msg, false, "Invalid directory path");
        return;
      }

      // 重置计数器
      processed_files_ = 0;
      successful_renames_ = 0;
      failed_renames_ = 0;

      // 收集所有文件
      std::vector<fs::path> files;
      try {
        for (const auto& entry : fs::directory_iterator(directory_path)) {
          if (entry.is_regular_file()) {
            files.push_back(entry.path());
          }
        }
      } catch (const fs::filesystem_error& e) {
        spdlog::error("Error reading directory: {}", e.what());
        sendFinalResponse(original_message, input_msg, false, e.what());
        return;
      }

      if (files.empty()) {
        spdlog::info("No files found in directory: {}", directory_path);
        sendFinalResponse(original_message, input_msg, true, "No files found");
        return;
      }

      // 提交所有文件重命名任务到线程池
      {
        std::lock_guard<std::mutex> lock(futures_mutex_);
        for (const auto& file_path : files) {
          auto future
              = thread_pool_->commit([this, file_path]() { renameFileThreadSafe(file_path); });
          pending_futures_.push_back(std::move(future));
        }
      }

      // 等待所有任务完成后发送最终响应
      event_loop_.runInLoop([this, original_message, input_msg, total_files = files.size()]() {
        waitForTasksAndSendFinalResponse(original_message, input_msg, total_files);
      });

      spdlog::info("Started processing {} files asynchronously", files.size());
    }

    /**
     * @brief 等待任务完成并发送最终响应
     */
    void waitForTasksAndSendFinalResponse(const zexuan::base::CommonMessage& original_message,
                                          const zexuan::base::Message& input_msg,
                                          size_t total_files) {
      // 在后台线程中等待任务完成，避免阻塞事件循环
      std::thread([this, original_message, input_msg, total_files]() {
        // 等待所有任务完成
        waitForAllTasks();

        // 发送最终响应
        std::string result_msg = "Processed " + std::to_string(total_files) + " files. "
                                 + "Success: " + std::to_string(successful_renames_.load()) + ", "
                                 + "Failed: " + std::to_string(failed_renames_.load());

        bool success = (failed_renames_.load() == 0);

        // 在事件循环中发送最终响应
        event_loop_.runInLoop([this, original_message, input_msg, success, result_msg]() {
          sendFinalResponse(original_message, input_msg, success, result_msg);
        });
      }).detach();
    }

    /**
     * @brief 发送最终响应
     */
    void sendFinalResponse(const zexuan::base::CommonMessage& original_message,
                           const zexuan::base::Message& input_msg, bool success,
                           const std::string& result_msg) {
      auto response = createBaseResponse(original_message, true);  // 最后一帧

      uint8_t cot = success ? 0x0A : 0x0B;  // 0x0A = 激活终止, 0x0B = 否定确认
      std::string response_content = success ? "RENAME_SUCCESS: " : "RENAME_FAILED: ";
      response_content += result_msg;

      auto response_msg = createResponse(input_msg, cot, response_content);

      std::vector<uint8_t> serialized_response;
      size_t size = response_msg.serialize(serialized_response);

      if (size > 0) {
        response.data = serialized_response;
      } else {
        response.data.assign(response_content.begin(), response_content.end());
      }

      if (sendResponse(response)) {
        spdlog::info("Sent final rename response: {}", response_content);
      } else {
        spdlog::error("Failed to send final rename response");
      }
    }

    /**
     * @brief 线程安全的文件重命名方法
     */
    void renameFileThreadSafe(const fs::path& file_path) {
      try {
        // 基于文件创建时间生成新的文件名
        std::string new_file_name
            = zexuan::utils::FileUtils::generateFileNameFromCreationTime(file_path.string());

        // 构建新的完整路径
        fs::path new_path = file_path.parent_path() / new_file_name;

        // 确保新文件名不存在（处理极少数UUID冲突情况）
        int counter = 1;
        while (fs::exists(new_path)) {
          std::string base_name = new_file_name.substr(0, new_file_name.find_last_of('.'));
          std::string ext = new_file_name.substr(new_file_name.find_last_of('.'));
          new_path = file_path.parent_path() / (base_name + "_" + std::to_string(counter) + ext);
          counter++;
        }

        // 执行文件重命名
        fs::rename(file_path, new_path);

        // 线程安全地更新计数器
        {
          std::lock_guard<std::mutex> lock(result_mutex_);
          successful_renames_++;
          spdlog::info("RenameProcessor: Renamed: {} -> {}", file_path.filename().string(),
                       new_path.filename().string());
        }

      } catch (const fs::filesystem_error& e) {
        std::lock_guard<std::mutex> lock(result_mutex_);
        failed_renames_++;
        spdlog::error("RenameProcessor: Failed to rename {}: {}", file_path.filename().string(),
                      e.what());
      } catch (const std::exception& e) {
        std::lock_guard<std::mutex> lock(result_mutex_);
        failed_renames_++;
        spdlog::error("RenameProcessor: Unexpected error renaming {}: {}",
                      file_path.filename().string(), e.what());
      }

      // 更新处理计数
      processed_files_++;
    }

  private:
    zexuan::event::EventLoop& event_loop_;                   ///< 事件循环引用
    std::unique_ptr<zexuan::base::ThreadPool> thread_pool_;  ///< 线程池
    std::mutex result_mutex_;                                ///< 结果互斥锁
    std::atomic<int> processed_files_;                       ///< 已处理文件数
    std::atomic<int> successful_renames_;                    ///< 成功重命名数
    std::atomic<int> failed_renames_;                        ///< 失败重命名数

    // 存储待完成的任务
    std::vector<std::future<void>> pending_futures_;  ///< 待完成的任务
    std::mutex futures_mutex_;                        ///< 任务互斥锁
  };

}  // namespace rename_client

#endif  // RENAME_CLIENT_RENAME_PROCESSOR_HPP
