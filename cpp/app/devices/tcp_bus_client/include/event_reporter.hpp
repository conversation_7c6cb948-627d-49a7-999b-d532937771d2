/**
 * @file event_reporter.hpp
 * @brief 事件上报器定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_EVENT_REPORTER_HPP
#define TCP_BUS_CLIENT_EVENT_REPORTER_HPP

#include <chrono>

#include "../../include/base_event_reporter.hpp"

namespace tcp_bus_client {

  /**
   * @brief 事件上报器
   *
   * 继承自BaseEventReporter，负责定时生成和发送时间戳事件到总线
   */
  class EventReporter : public devices::BaseEventReporter {
  public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用
     * @param interval_seconds 上报间隔（秒）
     */
    EventReporter(zexuan::bus::TcpBusClient& client, zexuan::event::EventLoop& event_loop,
                  double interval_seconds = 3.0)
        : devices::BaseEventReporter(client, event_loop, interval_seconds) {}

  protected:
    /**
     * @brief 生成并发送时间戳事件
     */
    void generateEvent() override {
      // 创建时间戳事件
      auto event_msg = createBaseEventMessage(1, "Automatic timestamp event from tcp_bus_client");

      // 添加当前时间戳作为数据
      auto now = std::chrono::system_clock::now();
      auto time_t = std::chrono::system_clock::to_time_t(now);
      std::string timestamp = std::to_string(time_t);
      event_msg.data.assign(timestamp.begin(), timestamp.end());

      // 发送事件到总线
      sendEventMessage(event_msg);
    }
  };

}  // namespace tcp_bus_client

#endif  // TCP_BUS_CLIENT_EVENT_REPORTER_HPP
