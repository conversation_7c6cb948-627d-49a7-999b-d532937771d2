/**
 * @file type1_processor.hpp
 * @brief Type1消息处理器定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_TYPE1_PROCESSOR_HPP
#define TCP_BUS_CLIENT_TYPE1_PROCESSOR_HPP

#include <spdlog/spdlog.h>

#include "message_processor.hpp"

namespace tcp_bus_client {

  /**
   * @brief Type1消息处理器
   *
   * 专门处理Type1类型的IEC103消息，实现单帧响应逻辑
   */
  class Type1Processor : public MessageProcessor {
  public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     */
    explicit Type1Processor(zexuan::bus::TcpBusClient& client) : MessageProcessor(client) {}

    /**
     * @brief 处理Type1消息
     * @param original_message 原始的CommonMessage
     * @param input_msg 解析后的IEC103消息
     * @return true表示处理成功，false表示处理失败
     */
    bool processMessage(const zexuan::base::CommonMessage& original_message,
                        const zexuan::base::Message& input_msg) override {
      spdlog::debug("Processing Type1 message - sending single response");

      // 创建响应消息
      auto response = createBaseResponse(original_message, true);  // 单帧响应，肯定是最后一帧

      // 创建IEC103响应消息
      std::string response_content = "SINGLE_RESPONSE_TO_" + input_msg.getTextContent();
      auto response_msg = createResponse(input_msg, 0x07, response_content);  // 0x07 = 激活确认

      // 序列化响应消息
      std::vector<uint8_t> serialized_response;
      size_t size = response_msg.serialize(serialized_response);

      if (size > 0) {
        response.data = serialized_response;
        spdlog::debug("Generated single IEC103 response message ({} bytes)", size);
      } else {
        // 序列化失败，使用简单响应
        std::string simple_response = "SINGLE_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());
        spdlog::warn("IEC103 serialization failed, using simple response");
      }

      // 发送响应到总线
      if (sendResponse(response)) {
        spdlog::debug("Sent single response to bus: invoke_id={}", response.invoke_id);
        return true;
      } else {
        spdlog::error("Failed to send single response to bus: invoke_id={}", response.invoke_id);
        return false;
      }
    }

    /**
     * @brief 检查是否可以处理指定类型的消息
     * @param message_type 消息类型（TYP字段）
     * @return true表示可以处理Type1消息
     */
    bool canProcess(uint8_t message_type) const override { return message_type == 1; }

    /**
     * @brief 获取处理器名称
     * @return 处理器名称
     */
    std::string getName() const override { return "Type1Processor"; }
  };

}  // namespace tcp_bus_client

#endif  // TCP_BUS_CLIENT_TYPE1_PROCESSOR_HPP
