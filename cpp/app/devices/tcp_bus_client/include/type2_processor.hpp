/**
 * @file type2_processor.hpp
 * @brief Type2消息处理器定义
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef TCP_BUS_CLIENT_TYPE2_PROCESSOR_HPP
#define TCP_BUS_CLIENT_TYPE2_PROCESSOR_HPP

#include <spdlog/spdlog.h>

#include "message_processor.hpp"
#include "zexuan/event/event_loop.hpp"

namespace tcp_bus_client {

  /**
   * @brief Type2消息处理器
   *
   * 专门处理Type2类型的IEC103消息，实现多帧响应逻辑
   */
  class Type2Processor : public MessageProcessor {
  public:
    /**
     * @brief 构造函数
     * @param client TCP总线客户端引用
     * @param event_loop 事件循环引用，用于定时发送第二帧
     */
    Type2Processor(zexuan::bus::TcpBusClient& client, zexuan::event::EventLoop& event_loop)
        : MessageProcessor(client), event_loop_(event_loop) {}

    /**
     * @brief 处理Type2消息
     * @param original_message 原始的CommonMessage
     * @param input_msg 解析后的IEC103消息
     * @return true表示处理成功，false表示处理失败
     */
    bool processMessage(const zexuan::base::CommonMessage& original_message,
                        const zexuan::base::Message& input_msg) override {
      spdlog::debug("Processing Type2 message - sending multi-frame response");

      // 发送第一帧
      if (!sendFirstFrame(original_message, input_msg)) {
        return false;
      }

      // 使用定时器延迟发送第二帧
      event_loop_.runAfter(0.1, [this, original_message, input_msg]() {
        sendSecondFrame(original_message, input_msg);
      });

      return true;
    }

    /**
     * @brief 检查是否可以处理指定类型的消息
     * @param message_type 消息类型（TYP字段）
     * @return true表示可以处理Type2消息
     */
    bool canProcess(uint8_t message_type) const override { return message_type == 2; }

    /**
     * @brief 获取处理器名称
     * @return 处理器名称
     */
    std::string getName() const override { return "Type2Processor"; }

  private:
    /**
     * @brief 发送第一帧响应
     * @param original_message 原始消息
     * @param input_msg 输入的IEC103消息
     * @return true表示发送成功，false表示发送失败
     */
    bool sendFirstFrame(const zexuan::base::CommonMessage& original_message,
                        const zexuan::base::Message& input_msg) {
      // 创建第一帧响应
      auto response1 = createBaseResponse(original_message, false);  // 不是最后一帧

      // 创建第一个IEC103响应消息
      std::string response_content1 = "MULTI_RESPONSE_1_TO_" + input_msg.getTextContent();
      auto response_msg1 = createResponse(input_msg, 0x07, response_content1);  // 0x07 = 激活确认

      // 序列化第一个响应消息
      std::vector<uint8_t> serialized_response1;
      size_t size1 = response_msg1.serialize(serialized_response1);

      if (size1 > 0) {
        response1.data = serialized_response1;
      } else {
        std::string simple_response1 = "MULTI_RESPONSE_1_FOR_" + original_message.invoke_id;
        response1.data.assign(simple_response1.begin(), simple_response1.end());
      }

      // 发送第一帧
      if (sendResponse(response1)) {
        spdlog::debug("Sent first frame of multi-response to bus: invoke_id={}",
                      response1.invoke_id);
        return true;
      } else {
        spdlog::error("Failed to send first frame of multi-response to bus: invoke_id={}",
                      response1.invoke_id);
        return false;
      }
    }

    /**
     * @brief 发送第二帧响应
     * @param original_message 原始消息
     * @param input_msg 输入的IEC103消息
     */
    void sendSecondFrame(const zexuan::base::CommonMessage& original_message,
                         const zexuan::base::Message& input_msg) {
      // 创建第二帧响应
      auto response2 = createBaseResponse(original_message, true);  // 这是最后一帧

      // 创建第二个IEC103响应消息
      std::string response_content2 = "MULTI_RESPONSE_2_TO_" + input_msg.getTextContent();
      auto response_msg2 = createResponse(input_msg, 0x0A, response_content2);  // 0x0A = 激活终止

      // 序列化第二个响应消息
      std::vector<uint8_t> serialized_response2;
      size_t size2 = response_msg2.serialize(serialized_response2);

      if (size2 > 0) {
        response2.data = serialized_response2;
      } else {
        std::string simple_response2 = "MULTI_RESPONSE_2_FOR_" + original_message.invoke_id;
        response2.data.assign(simple_response2.begin(), simple_response2.end());
      }

      // 发送第二帧
      if (sendResponse(response2)) {
        spdlog::debug("Sent second frame of multi-response to bus: invoke_id={}",
                      response2.invoke_id);
      } else {
        spdlog::error("Failed to send second frame of multi-response to bus: invoke_id={}",
                      response2.invoke_id);
      }
    }

    zexuan::event::EventLoop& event_loop_;  ///< 事件循环引用
  };

}  // namespace tcp_bus_client

#endif  // TCP_BUS_CLIENT_TYPE2_PROCESSOR_HPP
