/**
 * @file main.cpp
 * @brief IEC 60870-5-103 协议客户端主程序（现代化重构版本）
 * <AUTHOR>
 * @date 2024
 */

#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <thread>

#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"
#include "zexuan/base/logger_manager.hpp"
#include "zexuan/event/event_loop.hpp"

using namespace zexuan::protocol::client;

// 全局客户端实例
std::shared_ptr<ProtocolClient> g_client = nullptr;
std::shared_ptr<zexuan::event::EventLoop> g_event_loop = nullptr;
std::atomic<bool> g_running{true};
std::atomic<bool> g_connected{false};

// 消息发送计数器
std::atomic<int> g_message_counter{0};

// 全局客户端指针，用于在消息回调中访问客户端方法
std::shared_ptr<ProtocolClient> g_client_ptr = nullptr;

/**
 * @brief 连接状态变化回调
 */
void onConnectionStateChanged(bool connected) {
  if (connected) {
    spdlog::info("LocalClient -> Server is UP");
    g_connected = true;
    spdlog::info("Protocol test client connected, will send 103 test data automatically");
  } else {
    spdlog::info("LocalClient -> Server is DOWN");
    g_connected = false;
  }
}

/**
 * @brief 消息接收回调
 */
void onMessageReceived(const zexuan::base::Message& message) {
  spdlog::info(
      "Received message: TYP={:02X}, VSQ={:02X}, COT={:02X}, SRC={:02X}, TGT={:02X}, FUN={:02X}, "
      "INF={:02X}",
      message.getTyp(), message.getVsq(), message.getCot(), message.getSource(),
      message.getTarget(), message.getFun(), message.getInf());

  // 使用客户端的专门文本内容解析方法
  if (g_client_ptr) {
    std::string text_content = g_client_ptr->GetMessageTextContent(message);
    if (!text_content.empty()) {
      spdlog::info("Message text content: {}", text_content);
    }
  } else {
    // 回退到原始方法
    if (!message.getTextContent().empty()) {
      spdlog::info("Message text content: {}", message.getTextContent());
    }
  }
}

/**
 * @brief 消息发送定时器线程（每2秒发送一次，按顺序1,2,4,5）
 */
void messageTimerThread() {
  while (g_running && g_client) {
    if (g_connected && g_client->IsConnected()) {
      int message_type_index = (g_message_counter++ % 4);
      uint8_t message_type;

      switch (message_type_index) {
        case 0:
          message_type = 1;
          break;  // Type 1
        case 1:
          message_type = 2;
          break;  // Type 2
        case 2:
          message_type = 4;
          break;  // Type 4
        case 3:
          message_type = 5;
          break;  // Type 5
        default:
          message_type = 1;
          break;
      }

      // 使用新的简化API发送消息
      zexuan::base::Result<void> result;

      switch (message_type) {
        case 1:
          result = g_client->SendType1Message(9, "test data");
          break;
        case 2:
          result = g_client->SendType2Message(9, "multi frame test");
          break;
        case 4:
          result = g_client->SendType4Message(9);
          break;
        case 5:
          result = g_client->SendType5Message(10, "./files/");
          break;
        default:
          spdlog::error("Unknown message type: {}", message_type);
          continue;
      }

      if (!result) {
        spdlog::error("Failed to send message type {}", message_type);
      }
    }

    // 等待2秒，但要检查是否需要退出
    for (int i = 0; i < 20 && g_running; ++i) {
      std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
  }
}

int main(int argc, char* argv[]) {
  // 解析命令行参数
  std::string config_path = "./config/config.json";
  if (argc > 1) {
    config_path = argv[1];
  }

  // 初始化日志系统
  if (!zexuan::base::LoggerManager::initialize("protocol_client.log", config_path)) {
    std::cerr << "Failed to initialize logger" << std::endl;
    return -1;
  }

  spdlog::info("pid = {}", getpid());

  try {
    // 创建事件循环
    g_event_loop = std::make_shared<zexuan::event::EventLoop>();
    
    // 注册关闭信号
    g_event_loop->registerShutdownSignals([&](int signal) {
      spdlog::info("Received signal {}, shutting down gracefully...", signal);
      g_running.store(false);
      g_event_loop->quit();
    });
    
    // 创建客户端（传入EventLoop和配置文件路径）
    g_client = std::make_shared<ProtocolClient>(g_event_loop.get(), config_path);
    g_client_ptr = g_client;  // 设置全局指针供回调使用
    
    // 设置回调函数
    g_client->SetConnectionCallback(onConnectionStateChanged);
    g_client->SetMessageCallback(onMessageReceived);
    
    // 启动客户端
    auto start_result = g_client->Start();
    if (!start_result) {
      spdlog::error("Failed to start protocol client");
      return 1;
    }
    
    // 连接到服务器
    spdlog::info("Connecting to server...");
    auto connect_result = g_client->Connect();
    if (!connect_result) {
      spdlog::error("Failed to connect to server");
      return 1;
    }
    
    // 启动消息发送定时器线程
    std::thread timer_thread(messageTimerThread);
    
    // 运行事件循环（阻塞直到quit()被调用）
    g_event_loop->loop();
    spdlog::info("Event loop stopped");
    
    // 优雅关闭
    g_running = false;
    if (timer_thread.joinable()) {
      timer_thread.join();
    }
    
  } catch (const std::exception& e) {
    spdlog::error("Exception in main: {}", e.what());
    // 紧急清理
    if (g_client) {
      g_client->Stop();
      g_client.reset();
    }
    if (g_event_loop) {
      g_event_loop.reset();
    }
    return 1;
  }
  
  // 清理
  if (g_client) {
    auto stop_result = g_client->Stop();
    if (!stop_result) {
      spdlog::warn("Failed to stop client gracefully");
    }
    g_client.reset();
  }
  
  if (g_event_loop) {
    g_event_loop.reset();
  }
  
  return 0;
}