#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/protocol/protocol_server/server/protocol_server.hpp"

using namespace zexuan::protocol::server;
using namespace zexuan::base;
using namespace zexuan::event;

// 全局变量，用于信号处理
std::shared_ptr<ProtocolServer> g_server = nullptr;
std::shared_ptr<EventLoop> g_event_loop = nullptr;
std::atomic<bool> g_running{true};

int main(int argc, char* argv[]) {
  // 解析命令行参数
  std::string config_path = "./config/config.json";
  if (argc > 1) {
    config_path = argv[1];
  }

  // 初始化日志系统
  if (!LoggerManager::initialize("protocol_server.log", config_path)) {
    std::cerr << "Failed to initialize logger" << std::endl;
    return -1;
  }

  spdlog::info("Protocol Server starting...");
  spdlog::info("Using config file: {}", config_path);

  try {
    // 创建事件循环
    g_event_loop = std::make_shared<EventLoop>();
    spdlog::info("Event loop created");

    // 注册优雅关闭信号
    g_event_loop->registerShutdownSignals([&](int signal) {
      spdlog::info("Received signal {}, shutting down gracefully...", signal);
      g_running.store(false);
      g_event_loop->quit();
    });
    spdlog::info("Signal handlers registered");

    // 创建协议服务器
    g_server = std::make_shared<ProtocolServer>(g_event_loop.get(), config_path);
    spdlog::info("Protocol server created");

    // 启动服务器
    spdlog::info("Starting protocol server...");
    if (!g_server->Start()) {
      spdlog::error("Failed to start protocol server");
      return -1;
    }
    spdlog::info("Protocol server started successfully");

    spdlog::info("Server is running. Press Ctrl+C to stop.");

    // 运行事件循环（这会阻塞直到quit()被调用）
    g_event_loop->loop();

    spdlog::info("Event loop stopped");

    // 清理资源
    spdlog::info("Cleaning up resources...");
    if (g_server) {
      if (!g_server->Stop()) {
        spdlog::warn("Failed to stop protocol server gracefully");
      }
      g_server.reset();
    }

    if (g_event_loop) {
      g_event_loop.reset();
    }

    spdlog::info("Protocol Server shutdown completed");

  } catch (const std::exception& e) {
    spdlog::error("Exception in main: {}", e.what());

    // 异常情况下也要清理资源
    if (g_server) {
      spdlog::info("Emergency cleanup: stopping protocol server...");
      g_server->Stop();
      g_server.reset();
    }

    if (g_event_loop) {
      g_event_loop.reset();
    }

    return -1;
  }

  return 0;
}