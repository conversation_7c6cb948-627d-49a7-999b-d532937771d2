# 开发计划书：Protocol Client 极简化重构

## 1. 项目概述

**项目目标：** 将复杂的 protocol_client 重构为极简的单文件实现，只保留核心功能：连接服务端、发送消息、接收消息、消息解析。

**重构范围：**
- 合并多个文件为单一的 `protocol_client.hpp/cpp`
- 移除复杂的错误处理系统，统一使用 `base::ErrorCode`
- 直接集成 `zexuan::event::TcpClient`
- 保留 handlers 的消息生成和解析逻辑
- 移除 `MessageRequest` 结构，简化API，直接使用message.hpp解析

## 2. 任务分解与实施计划

### 阶段一：架构设计与接口定义

**[ ] 任务1：分析现有handlers接口依赖** 👤
- **描述：** 详细分析type1-type5 handlers的当前接口，确定如何在移除MessageRequest后保持功能完整性
- **验收标准：** 完成handlers接口分析文档，明确每个handler需要的参数类型
- **预期成果：** handlers接口适配方案
- **依赖：** 无

**[ ] 任务2：设计新的ProtocolClient API接口**
- **描述：** 设计极简的API接口，包括连接管理、消息发送、回调设置等核心功能
- **验收标准：** 完成新API设计，接口清晰简洁，满足所有核心需求
- **预期成果：** protocol_client.hpp 头文件框架
- **依赖：** 任务1

### 阶段二：核心实现

**[ ] 任务3：实现ProtocolClient头文件**
- **描述：** 创建 protocol_client.hpp，定义类结构、公共接口、回调类型等
- **验收标准：** 头文件编译通过，接口定义完整
- **预期成果：** 完整的头文件定义
- **依赖：** 任务2

**[ ] 任务4：实现TCP连接管理功能**
- **描述：** 在protocol_client.cpp中实现基于zexuan::event::TcpClient的连接管理
- **验收标准：** 连接、断开、状态管理功能正常工作
- **预期成果：** 连接管理模块
- **依赖：** 任务3

**[ ] 任务5：实现消息发送功能**
- **描述：** 集成handlers，实现各类型消息的发送功能，移除MessageRequest依赖
- **验收标准：** 能够通过简化的参数创建并发送type1-type5消息
- **预期成果：** 消息发送模块
- **依赖：** 任务4

**[ ] 任务6：实现消息接收与解析功能**
- **描述：** 实现基于base::Message的消息接收、解析和回调分发
- **验收标准：** 能够正确接收、解析消息并触发相应回调
- **预期成果：** 消息接收解析模块
- **依赖：** 任务5

### 阶段三：Handlers适配

**[ ] 任务7：适配Type1Handler接口** 👤
- **描述：** 修改Type1Handler的调用方式，移除MessageRequest依赖，提供直接的参数传递
- **验收标准：** Type1Handler能够通过新接口正常工作
- **预期成果：** 适配后的Type1Handler集成
- **依赖：** 任务6

**[ ] 任务8：适配Type2Handler接口**
- **描述：** 修改Type2Handler的调用方式，移除MessageRequest依赖
- **验收标准：** Type2Handler能够通过新接口正常工作
- **预期成果：** 适配后的Type2Handler集成
- **依赖：** 任务7

**[ ] 任务9：适配Type3Handler接口**
- **描述：** 修改Type3Handler的调用方式，移除MessageRequest依赖
- **验收标准：** Type3Handler能够通过新接口正常工作
- **预期成果：** 适配后的Type3Handler集成
- **依赖：** 任务8

**[ ] 任务10：适配Type4Handler接口**
- **描述：** 修改Type4Handler的调用方式，移除MessageRequest依赖
- **验收标准：** Type4Handler能够通过新接口正常工作
- **预期成果：** 适配后的Type4Handler集成
- **依赖：** 任务9

**[ ] 任务11：适配Type5Handler接口**
- **描述：** 修改Type5Handler的调用方式，移除MessageRequest依赖
- **验收标准：** Type5Handler能够通过新接口正常工作
- **预期成果：** 适配后的Type5Handler集成
- **依赖：** 任务10

### 阶段四：清理与整合

**[ ] 任务12：移除client_types相关文件**
- **描述：** 删除client_types.hpp/cpp文件，移除ClientErrorCode相关代码
- **验收标准：** 相关文件被删除，代码中不再有ClientErrorCode引用
- **预期成果：** 清理后的代码库
- **依赖：** 任务11

**[ ] 任务13：移除TcpConnectionManager**
- **描述：** 删除tcp_connection_manager相关文件
- **验收标准：** 相关文件被删除，功能已迁移到ProtocolClient中
- **预期成果：** 简化的代码结构
- **依赖：** 任务12

**[ ] 任务14：移除MessageFrameParser**
- **描述：** 删除message_frame_parser相关文件，功能集成到ProtocolClient
- **验收标准：** 相关文件被删除，解析功能正常工作
- **预期成果：** 进一步简化的代码结构
- **依赖：** 任务13

**[ ] 任务15：移除MessageTypeHandlerFactory**
- **描述：** 删除message_type_handler_factory相关文件，直接在ProtocolClient中管理handlers
- **验收标准：** 相关文件被删除，handlers管理功能正常
- **预期成果：** 最终简化的代码结构
- **依赖：** 任务14

**[ ] 任务16：移除旧的ProtocolClientImpl**
- **描述：** 删除protocol_client_impl相关文件，功能已迁移到新的ProtocolClient
- **验收标准：** 旧实现文件被删除，新实现功能完整
- **预期成果：** 完全重构的代码库
- **依赖：** 任务15

### 阶段五：构建与测试

**[ ] 任务17：更新CMakeLists.txt** 👤
- **描述：** 更新构建配置，移除已删除文件的引用，添加新文件
- **验收标准：** 项目能够正常编译
- **预期成果：** 更新的构建配置
- **依赖：** 任务16

**[ ] 任务18：编写单元测试** 👤
- **描述：** 为新的ProtocolClient编写单元测试，验证各项功能
- **验收标准：** 测试覆盖所有核心功能，测试通过率100%
- **预期成果：** 完整的测试套件
- **依赖：** 任务17

**[ ] 任务19：编写集成测试** 👤
- **描述：** 编写端到端的集成测试，验证与实际服务端的通信
- **验收标准：** 能够成功连接服务端并完成消息收发
- **预期成果：** 集成测试验证
- **依赖：** 任务18

**[ ] 任务20：性能测试与优化** 👤
- **描述：** 进行性能测试，确保重构后性能不低于原实现
- **验收标准：** 性能指标满足要求
- **预期成果：** 性能测试报告
- **依赖：** 任务19

## 3. 关键设计决策

### 3.1 新API设计概览
```cpp
class ProtocolClient {
public:
    // 基本生命周期
    base::Result<void> Start();
    base::Result<void> Stop();
    base::Result<void> Connect();
    void Disconnect();
    bool IsConnected() const;

    // 消息发送 - 为每种类型提供专门的方法
    base::Result<void> SendType1Message(uint8_t target, const std::string& test_data = "");
    base::Result<void> SendType2Message(uint8_t target, const std::string& test_data = "");
    base::Result<void> SendType3Message(uint8_t target);
    base::Result<void> SendType4Message(uint8_t target);
    base::Result<void> SendType5Message(uint8_t target, const std::string& new_name);

    // 回调设置
    void SetConnectionCallback(std::function<void(bool connected)> callback);
    void SetMessageCallback(std::function<void(const base::Message&)> callback);
};
```

### 3.2 错误处理简化
- 完全移除 `ClientErrorCode` 枚举
- 统一使用 `base::ErrorCode`
- 使用 `base::Result<T>` 作为返回类型

### 3.3 Handlers集成方式
- 直接在ProtocolClient中实例化所有handlers
- 移除工厂模式，简化handlers管理
- 为每种消息类型提供专门的发送方法

## 4. 风险识别与应对

**风险1：** Handlers接口适配复杂度超预期
- **应对：** 优先分析handlers依赖，必要时保留部分MessageRequest字段

**风险2：** 性能下降
- **应对：** 在任务20中进行性能对比测试，及时优化

**风险3：** 兼容性问题
- **应对：** 保持对外接口的语义一致性，确保调用方无需大幅修改

## 5. 验证方法

每个任务完成后需要进行以下验证：
1. **编译验证：** 代码能够正常编译
2. **功能验证：** 核心功能按预期工作
3. **接口验证：** API接口符合设计要求
4. **集成验证：** 与现有系统集成无问题

## 6. 预期成果

**最终交付物：**
1. `protocol_client.hpp` - 简化的头文件
2. `protocol_client.cpp` - 核心实现
3. 保留的 `handlers/` 目录（type1-type5）
4. 更新的构建配置
5. 完整的测试套件

**代码行数预期：** 从当前的约2000行代码简化到约800行代码

---

## 实施说明

- 标记为 👤 的任务需要人工参与和决策
- 每个任务完成后需要进行相应的验证
- 建议按阶段顺序执行，确保依赖关系正确
- 重构过程中保持代码的可编译状态