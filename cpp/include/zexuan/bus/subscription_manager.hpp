/**
 * @file subscription_manager.hpp
 * @brief 订阅管理器 - 管理客户端订阅信息和消息路由规则
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BUS_SUBSCRIPTION_MANAGER_HPP
#define ZEXUAN_BUS_SUBSCRIPTION_MANAGER_HPP

#include <memory>
#include <mutex>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "zexuan/base/types/structs.hpp"
#include "zexuan/event/net/tcp_connection.hpp"
#include "zexuan/utils/message_serializer.hpp"

namespace zexuan {
  namespace bus {

    /**
     * @brief 客户端订阅信息
     */
    struct ClientSubscription {
      std::unordered_set<int> message_types;                       ///< 订阅的消息类型
      std::unordered_set<int> event_types;                         ///< 订阅的事件类型
      std::string client_name;                                     ///< 客户端名称（可选）
      zexuan::base::ObjectId client_id{zexuan::base::INVALID_ID};  ///< 客户端ID，用于点对点消息路由

      ClientSubscription() = default;

      /**
       * @brief 检查是否订阅了指定的消息类型
       */
      bool isSubscribedToMessageType(int message_type) const {
        return message_types.find(message_type) != message_types.end();
      }

      /**
       * @brief 检查是否订阅了指定的事件类型
       */
      bool isSubscribedToEventType(int event_type) const {
        return event_types.find(event_type) != event_types.end();
      }

      /**
       * @brief 检查是否有任何订阅
       */
      bool hasAnySubscription() const { return !message_types.empty() || !event_types.empty(); }
    };

    /**
     * @brief 订阅管理器类
     *
     * 负责管理所有客户端的订阅信息，提供消息路由功能
     */
    class SubscriptionManager {
    public:
      using TcpConnectionPtr = std::shared_ptr<zexuan::event::TcpConnection>;
      using ConnectionList = std::vector<TcpConnectionPtr>;

      SubscriptionManager() = default;
      ~SubscriptionManager() = default;

      // 禁用拷贝构造和赋值
      SubscriptionManager(const SubscriptionManager&) = delete;
      SubscriptionManager& operator=(const SubscriptionManager&) = delete;

      /**
       * @brief 添加客户端连接
       * @param conn TCP连接
       * @param client_name 客户端名称（可选）
       */
      void addClient(const TcpConnectionPtr& conn, const std::string& client_name = "");

      /**
       * @brief 移除客户端连接
       * @param conn TCP连接
       */
      void removeClient(const TcpConnectionPtr& conn);

      /**
       * @brief 处理客户端订阅请求
       * @param conn TCP连接
       * @param subscription_msg 订阅消息
       * @return 处理结果的控制消息
       */
      zexuan::base::ControlMessage handleSubscription(
          const TcpConnectionPtr& conn, const zexuan::base::SubscriptionMessage& subscription_msg);

      /**
       * @brief 获取订阅了指定消息类型的客户端列表
       * @param message_type 消息类型
       * @return 客户端连接列表
       */
      ConnectionList getSubscribersForMessageType(int message_type) const;

      /**
       * @brief 获取订阅了指定事件类型的客户端列表
       * @param event_type 事件类型
       * @return 客户端连接列表
       */
      ConnectionList getSubscribersForEventType(int event_type) const;

      /**
       * @brief 获取指定客户端的订阅信息
       * @param conn TCP连接
       * @return 订阅信息，如果客户端不存在返回nullptr
       */
      std::shared_ptr<ClientSubscription> getClientSubscription(const TcpConnectionPtr& conn) const;

      /**
       * @brief 获取所有客户端数量
       * @return 客户端数量
       */
      size_t getClientCount() const;

      /**
       * @brief 获取所有活跃连接
       * @return 所有客户端连接列表
       */
      ConnectionList getAllClients() const;

      /**
       * @brief 清理无效连接
       * @return 清理的连接数量
       */
      size_t cleanupInvalidConnections();

      /**
       * @brief 获取统计信息
       */
      struct Statistics {
        size_t total_clients{0};
        size_t total_message_subscriptions{0};
        size_t total_event_subscriptions{0};
        std::unordered_map<int, size_t> message_type_subscribers;  ///< 每种消息类型的订阅者数量
        std::unordered_map<int, size_t> event_type_subscribers;    ///< 每种事件类型的订阅者数量
      };

      /**
       * @brief 获取统计信息
       * @return 统计信息
       */
      Statistics getStatistics() const;

      /**
       * @brief 根据客户端ID查找连接
       * @param client_id 客户端ID
       * @return TCP连接，未找到返回nullptr
       */
      TcpConnectionPtr findConnectionByClientId(zexuan::base::ObjectId client_id) const;

    private:
      mutable std::mutex mutex_;  ///< 保护数据结构的互斥锁

      /// 客户端订阅信息映射：连接 -> 订阅信息
      std::unordered_map<TcpConnectionPtr, std::shared_ptr<ClientSubscription>>
          client_subscriptions_;

      /// 消息类型订阅者映射：消息类型 -> 订阅者列表
      std::unordered_map<int, std::unordered_set<TcpConnectionPtr>> message_type_subscribers_;

      /// 事件类型订阅者映射：事件类型 -> 订阅者列表
      std::unordered_map<int, std::unordered_set<TcpConnectionPtr>> event_type_subscribers_;

      /// 客户端ID到连接的映射：client_id -> 连接
      std::unordered_map<zexuan::base::ObjectId, TcpConnectionPtr> client_id_to_connection_;

      /**
       * @brief 添加消息类型订阅（内部方法，不加锁）
       * @param conn TCP连接
       * @param message_types 消息类型列表
       */
      void addMessageTypeSubscriptions(const TcpConnectionPtr& conn,
                                       const std::vector<int>& message_types);

      /**
       * @brief 移除消息类型订阅（内部方法，不加锁）
       * @param conn TCP连接
       * @param message_types 消息类型列表
       */
      void removeMessageTypeSubscriptions(const TcpConnectionPtr& conn,
                                          const std::vector<int>& message_types);

      /**
       * @brief 添加事件类型订阅（内部方法，不加锁）
       * @param conn TCP连接
       * @param event_types 事件类型列表
       */
      void addEventTypeSubscriptions(const TcpConnectionPtr& conn,
                                     const std::vector<int>& event_types);

      /**
       * @brief 移除事件类型订阅（内部方法，不加锁）
       * @param conn TCP连接
       * @param event_types 事件类型列表
       */
      void removeEventTypeSubscriptions(const TcpConnectionPtr& conn,
                                        const std::vector<int>& event_types);

      /**
       * @brief 清理客户端的所有订阅（内部方法，不加锁）
       * @param conn TCP连接
       */
      void cleanupClientSubscriptions(const TcpConnectionPtr& conn);
    };

  }  // namespace bus
}  // namespace zexuan

#endif  // ZEXUAN_BUS_SUBSCRIPTION_MANAGER_HPP
