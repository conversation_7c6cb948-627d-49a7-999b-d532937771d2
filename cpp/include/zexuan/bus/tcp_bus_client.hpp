/**
 * @file tcp_bus_client.hpp
 * @brief TCP消息总线客户端类
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BUS_TCP_BUS_CLIENT_HPP
#define ZEXUAN_BUS_TCP_BUS_CLIENT_HPP

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/base/types/structs.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/tcp_client.hpp"
#include "zexuan/utils/message_protocol.hpp"
#include "zexuan/utils/message_serializer.hpp"

namespace zexuan {
  namespace bus {

    /**
     * @brief 消息回调函数类型
     */
    using CommonMessageCallback = std::function<void(const zexuan::base::CommonMessage&)>;
    using EventMessageCallback = std::function<void(const zexuan::base::EventMessage&)>;
    using ControlMessageCallback = std::function<void(const zexuan::base::ControlMessage&)>;

    /**
     * @brief TCP消息总线客户端类
     *
     * 提供连接到TCP消息总线服务器、发送消息、订阅消息等功能
     */
    class TcpBusClient : public base::LifecycleComponentBase {
    public:
      using TcpConnectionPtr = std::shared_ptr<zexuan::event::TcpConnection>;

      /**
       * @brief 构造函数
       * @param config_file_path 配置文件路径
       * @param event_loop 事件循环
       * @param client_name 客户端名称
       * @param client_id 客户端ID（用于点对点消息，可选）
       */
      TcpBusClient(const std::string& config_file_path, zexuan::event::EventLoop* event_loop,
                   const std::string& client_name = "",
                   zexuan::base::ObjectId client_id = zexuan::base::INVALID_ID);

      ~TcpBusClient();

      // 禁用拷贝构造和赋值
      TcpBusClient(const TcpBusClient&) = delete;
      TcpBusClient& operator=(const TcpBusClient&) = delete;

      // 生命周期管理 - 实现ILifecycleComponent接口
      bool Start() override;
      bool Stop() override;

      /**
       * @brief 检查是否已连接
       * @return 是否已连接
       */
      bool isConnected() const;

      /**
       * @brief 订阅消息类型
       * @param message_types 消息类型列表
       * @param event_types 事件类型列表
       * @return 是否发送订阅请求成功
       */
      bool subscribe(const std::vector<int>& message_types,
                     const std::vector<int>& event_types = {});

      /**
       * @brief 订阅消息类型（带客户端ID）
       * @param message_types 消息类型列表
       * @param event_types 事件类型列表
       * @param client_id 客户端ID，用于点对点通信
       * @return 是否发送订阅请求成功
       */
      bool subscribe(const std::vector<int>& message_types, const std::vector<int>& event_types,
                     zexuan::base::ObjectId client_id);

      /**
       * @brief 取消订阅消息类型
       * @param message_types 消息类型列表
       * @param event_types 事件类型列表
       * @return 是否发送取消订阅请求成功
       */
      bool unsubscribe(const std::vector<int>& message_types,
                       const std::vector<int>& event_types = {});

      /**
       * @brief 发送CommonMessage
       * @param message CommonMessage对象
       * @return 是否发送成功
       */
      bool sendCommonMessage(const zexuan::base::CommonMessage& message);

      /**
       * @brief 发送EventMessage
       * @param message EventMessage对象
       * @return 是否发送成功
       */
      bool sendEventMessage(const zexuan::base::EventMessage& message);

      /**
       * @brief 设置CommonMessage回调
       * @param callback 回调函数
       */
      void setCommonMessageCallback(const CommonMessageCallback& callback) {
        common_message_callback_ = callback;
      }

      /**
       * @brief 设置EventMessage回调
       * @param callback 回调函数
       */
      void setEventMessageCallback(const EventMessageCallback& callback) {
        event_message_callback_ = callback;
      }

      /**
       * @brief 设置ControlMessage回调
       * @param callback 回调函数
       */
      void setControlMessageCallback(const ControlMessageCallback& callback) {
        control_message_callback_ = callback;
      }

      /**
       * @brief 获取客户端名称
       * @return 客户端名称
       */
      const std::string& getClientName() const { return client_name_; }

      /**
       * @brief 获取客户端ID
       * @return 客户端ID
       */
      zexuan::base::ObjectId getClientId() const { return client_id_; }

      /**
       * @brief 设置客户端ID
       * @param client_id 客户端ID
       */
      void setClientId(zexuan::base::ObjectId client_id) { client_id_ = client_id; }

      /**
       * @brief 获取服务器地址
       * @return 服务器地址字符串
       */
      std::string getServerAddress() const;

    private:
      std::string config_file_path_;          ///< 配置文件路径
      zexuan::event::EventLoop* event_loop_;  ///< 事件循环
      std::string server_host_;               ///< 服务器地址
      uint16_t server_port_;                  ///< 服务器端口
      std::string client_name_;               ///< 客户端名称
      zexuan::base::ObjectId client_id_;      ///< 客户端ID，用于点对点消息路由

      std::unique_ptr<zexuan::event::TcpClient> tcp_client_;          ///< TCP客户端
      std::unique_ptr<zexuan::utils::MessageBuffer> message_buffer_;  ///< 消息缓冲区
      std::atomic<bool> connected_{false};                            ///< 连接状态

      // 消息回调函数
      CommonMessageCallback common_message_callback_;
      EventMessageCallback event_message_callback_;
      ControlMessageCallback control_message_callback_;

      mutable std::mutex mutex_;  ///< 保护共享数据的互斥锁

      /**
       * @brief 连接状态回调
       * @param conn TCP连接
       */
      void onConnection(const TcpConnectionPtr& conn);

      /**
       * @brief 消息接收回调
       * @param conn TCP连接
       * @param buf 接收缓冲区
       * @param receive_time 接收时间
       */
      void onMessage(const TcpConnectionPtr& conn, zexuan::event::Buffer* buf,
                     zexuan::event::Timestamp receive_time);

      /**
       * @brief 处理完整的JSON消息
       * @param json_message JSON消息内容
       */
      void handleJsonMessage(const std::string& json_message);

      /**
       * @brief 发送订阅消息
       * @param action 订阅动作
       * @param message_types 消息类型列表
       * @param event_types 事件类型列表
       * @return 是否发送成功
       */
      bool sendSubscriptionMessage(zexuan::base::SubscriptionMessage::Action action,
                                   const std::vector<int>& message_types,
                                   const std::vector<int>& event_types);

      /**
       * @brief 发送JSON消息
       * @param json_message JSON消息内容
       * @return 是否发送成功
       */
      bool sendJsonMessage(const std::string& json_message);

      /**
       * @brief 判断消息类型
       * @param json_str JSON字符串
       * @return 消息类型字符串
       */
      std::string getMessageCategory(const std::string& json_str);

      /**
       * @brief 加载配置文件
       * @return 是否加载成功
       */
      bool LoadConfiguration();
    };

  }  // namespace bus
}  // namespace zexuan

#endif  // ZEXUAN_BUS_TCP_BUS_CLIENT_HPP
