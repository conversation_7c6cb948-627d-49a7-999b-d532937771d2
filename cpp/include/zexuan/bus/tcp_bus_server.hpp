/**
 * @file tcp_message_bus.hpp
 * @brief TCP消息总线服务器类
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_BUS_TCP_MESSAGE_BUS_HPP
#define ZEXUAN_BUS_TCP_MESSAGE_BUS_HPP

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>

#include "nlohmann/json.hpp"
#include "zexuan/base/lifecycle_component.hpp"
#include "zexuan/bus/message_router.hpp"
#include "zexuan/bus/subscription_manager.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/tcp_server.hpp"
#include "zexuan/utils/message_protocol.hpp"

namespace zexuan {
  namespace bus {

    /**
     * @brief TCP消息总线服务器类
     *
     * 整合连接管理、订阅管理、消息路由等功能，提供完整的TCP消息总线服务
     */
    class TcpBusServer : public base::LifecycleComponentBase {
    public:
      using TcpConnectionPtr = std::shared_ptr<zexuan::event::TcpConnection>;

      /**
       * @brief 构造函数
       * @param event_loop 事件循环
       * @param config_path 配置文件路径
       */
      TcpBusServer(zexuan::event::EventLoop* event_loop, const std::string& config_path);

      ~TcpBusServer();

      // 禁用拷贝构造和赋值
      TcpBusServer(const TcpBusServer&) = delete;
      TcpBusServer& operator=(const TcpBusServer&) = delete;

      // 生命周期管理 - 实现ILifecycleComponent接口
      bool Start() override;
      bool Stop() override;

      /**
       * @brief 检查是否正在运行
       * @return 是否运行中
       */
      bool isRunning() const { return running_.load(); }

      /**
       * @brief 获取当前连接数
       * @return 连接数
       */
      size_t getConnectionCount() const;

      /**
       * @brief 获取订阅管理器统计信息
       * @return 统计信息
       */
      SubscriptionManager::Statistics getSubscriptionStatistics() const;

      /**
       * @brief 获取路由统计信息
       * @return 统计信息
       */
      RoutingStatistics getRoutingStatistics() const;

      /**
       * @brief 重置统计信息
       */
      void resetStatistics();

      /**
       * @brief 清理无效连接
       * @return 清理的连接数
       */
      size_t cleanupInvalidConnections();

      /**
       * @brief 广播系统消息
       * @param message 系统消息内容
       * @return 成功发送的客户端数量
       */
      size_t broadcastSystemMessage(const std::string& message);

    private:
      zexuan::event::EventLoop* event_loop_;  ///< 事件循环
      std::string config_path_;               ///< 配置文件路径
      std::atomic<bool> running_{false};      ///< 运行状态

      std::unique_ptr<zexuan::event::TcpServer> tcp_server_;       ///< TCP服务器
      std::shared_ptr<SubscriptionManager> subscription_manager_;  ///< 订阅管理器
      std::shared_ptr<MessageRouter> message_router_;              ///< 消息路由器

      /// 客户端消息缓冲区映射：连接 -> 消息缓冲区
      std::unordered_map<TcpConnectionPtr, std::unique_ptr<zexuan::utils::MessageBuffer>>
          client_buffers_;
      mutable std::mutex buffers_mutex_;  ///< 保护缓冲区映射的互斥锁

      /**
       * @brief 新连接回调
       * @param conn TCP连接
       */
      void onConnection(const TcpConnectionPtr& conn);

      /**
       * @brief 消息接收回调
       * @param conn TCP连接
       * @param buf 接收缓冲区
       * @param receive_time 接收时间
       */
      void onMessage(const TcpConnectionPtr& conn, zexuan::event::Buffer* buf,
                     zexuan::event::Timestamp receive_time);

      /**
       * @brief 处理完整的JSON消息
       * @param conn TCP连接
       * @param json_message JSON消息内容
       */
      void handleJsonMessage(const TcpConnectionPtr& conn, const std::string& json_message);

      /**
       * @brief 处理订阅消息
       * @param conn TCP连接
       * @param json_str JSON字符串
       */
      void handleSubscriptionMessage(const TcpConnectionPtr& conn, const std::string& json_str);

      /**
       * @brief 处理CommonMessage
       * @param conn TCP连接
       * @param json_str JSON字符串
       */
      void handleCommonMessage(const TcpConnectionPtr& conn, const std::string& json_str);

      /**
       * @brief 处理EventMessage
       * @param conn TCP连接
       * @param json_str JSON字符串
       */
      void handleEventMessage(const TcpConnectionPtr& conn, const std::string& json_str);

      /**
       * @brief 加载配置文件
       * @return 是否加载成功
       */
      bool loadConfig();

      /**
       * @brief 获取或创建客户端消息缓冲区
       * @param conn TCP连接
       * @return 消息缓冲区指针
       */
      zexuan::utils::MessageBuffer* getOrCreateBuffer(const TcpConnectionPtr& conn);

      /**
       * @brief 移除客户端消息缓冲区
       * @param conn TCP连接
       */
      void removeBuffer(const TcpConnectionPtr& conn);

      /**
       * @brief 发送错误响应
       * @param conn TCP连接
       * @param error_message 错误信息
       */
      void sendErrorResponse(const TcpConnectionPtr& conn, const std::string& error_message);

      /**
       * @brief 判断消息类型
       * @param json_str JSON字符串
       * @return 消息类型字符串
       */
      std::string getMessageCategory(const std::string& json_str);

      // 配置参数
      std::string host_;
      uint16_t port_;
      int thread_pool_size_;
      bool verbose_logging_;
      size_t max_connections_;
    };

  }  // namespace bus
}  // namespace zexuan

#endif  // ZEXUAN_BUS_TCP_MESSAGE_BUS_HPP
