#ifndef ZEXUAN_EVENT_TCP_CONNECTION_HPP
#define ZEXUAN_EVENT_TCP_CONNECTION_HPP

#include <any>
#include <memory>
#include <string_view>

#include "zexuan/event/callbacks.hpp"
#include "zexuan/event/net/address.hpp"
#include "zexuan/event/net/buffer.hpp"
#include "zexuan/event/net/socket.hpp"

// struct tcp_info is in <netinet/tcp.h>
struct tcp_info;

namespace zexuan {
  namespace event {

    class Channel;
    class EventLoop;
    class Socket;

    ///
    /// TCP connection, for both client and server usage.
    ///
    /// This is an interface class, so don't expose too much details.
    class TcpConnection : public std::enable_shared_from_this<TcpConnection> {
    public:
      /// Constructs a TcpConnection with a connected sockfd
      ///
      /// User should not create this object.
      TcpConnection(EventLoop* loop, const std::string& name, int sockfd,
                    const zexuan::event::Address& localAddr,
                    const zexuan::event::Address& peerAddr);
      ~TcpConnection();

      // 禁用拷贝构造和赋值
      TcpConnection(const TcpConnection&) = delete;
      TcpConnection& operator=(const TcpConnection&) = delete;

      EventLoop* getLoop() const noexcept { return loop_; }
      const std::string& name() const noexcept { return name_; }
      const zexuan::event::Address& localAddress() const noexcept { return localAddr_; }
      const zexuan::event::Address& peerAddress() const noexcept { return peerAddr_; }
      bool connected() const noexcept { return state_ == kConnected; }
      bool disconnected() const noexcept { return state_ == kDisconnected; }
      // return true if success.
      bool getTcpInfo(struct tcp_info*) const;
      std::string getTcpInfoString() const;

      // void send(string&& message); // C++11
      void send(const void* message, int len);
      void send(const std::string_view& message);
      // void send(Buffer&& message); // C++11
      void send(Buffer* message);  // this one will swap data
      void shutdown();             // NOT thread safe, no simultaneous calling
      // void shutdownAndForceCloseAfter(double seconds); // NOT thread safe, no simultaneous
      // calling
      void forceClose();
      void forceCloseWithDelay(double seconds);
      void setTcpNoDelay(bool on);
      // reading or not
      void startRead();
      void stopRead();
      bool isReading() const noexcept {
        return reading_;
      };  // NOT thread safe, may race with start/stopReadInLoop

      void setContext(const std::any& context) { context_ = context; }

      const std::any& getContext() const noexcept { return context_; }

      std::any* getMutableContext() noexcept { return &context_; }

      void setConnectionCallback(const ConnectionCallback& cb) { connectionCallback_ = cb; }

      void setMessageCallback(const MessageCallback& cb) { messageCallback_ = cb; }

      void setWriteCompleteCallback(const WriteCompleteCallback& cb) {
        writeCompleteCallback_ = cb;
      }

      void setHighWaterMarkCallback(const HighWaterMarkCallback& cb, size_t highWaterMark) {
        highWaterMarkCallback_ = cb;
        highWaterMark_ = highWaterMark;
      }

      /// Advanced interface
      Buffer* inputBuffer() noexcept { return &inputBuffer_; }

      Buffer* outputBuffer() noexcept { return &outputBuffer_; }

      /// Internal use only.
      void setCloseCallback(const CloseCallback& cb) { closeCallback_ = cb; }

      // called when TcpServer accepts a new connection
      void connectEstablished();  // should be called only once
      // called when TcpServer has removed me from its map
      void connectDestroyed();  // should be called only once

    private:
      enum StateE { kDisconnected, kConnecting, kConnected, kDisconnecting };
      void handleRead(Timestamp receiveTime);
      void handleWrite();
      void handleClose();
      void handleError();
      void sendInLoop(const void* message, size_t len);
      void sendInLoop(const std::string_view& message);
      void shutdownInLoop();
      // void shutdownAndForceCloseInLoop(double seconds);
      void forceCloseInLoop();
      void setState(StateE s) { state_ = s; }
      const char* stateToString() const;
      void startReadInLoop();
      void stopReadInLoop();

      EventLoop* loop_;
      const std::string name_;
      StateE state_;  // FIXME: use atomic variable
      bool reading_;
      // we don't expose those classes to client.
      std::unique_ptr<zexuan::event::Socket> socket_;
      std::unique_ptr<Channel> channel_;
      const zexuan::event::Address localAddr_;
      const zexuan::event::Address peerAddr_;
      std::any context_;

      ConnectionCallback connectionCallback_;
      MessageCallback messageCallback_;
      WriteCompleteCallback writeCompleteCallback_;
      HighWaterMarkCallback highWaterMarkCallback_;
      CloseCallback closeCallback_;
      size_t highWaterMark_;

      Buffer inputBuffer_;
      Buffer outputBuffer_;  // FIXME: use list<Buffer> as output buffer.
                             // FIXME: creationTime_, lastReceiveTime_
                             //        bytesReceived_, bytesSent_
                             //        should be "volatile"
                             // FIXME: add http context, codec context, etc.
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_TCP_CONNECTION_HPP
