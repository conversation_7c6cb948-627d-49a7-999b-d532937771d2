#ifndef ZEXUAN_EVENT_TCP_CONNECTOR_HPP
#define ZEXUAN_EVENT_TCP_CONNECTOR_HPP

#include <functional>
#include <memory>

#include "zexuan/event/callbacks.hpp"
#include "zexuan/event/channel.hpp"
#include "zexuan/event/net/address.hpp"
#include "zexuan/event/sys/timer_handler.hpp"

namespace zexuan {
  namespace event {

    class EventLoop;

    class TcpConnector : public std::enable_shared_from_this<TcpConnector> {
    public:
      TcpConnector(EventLoop* loop, const zexuan::event::Address& serverAddr);
      ~TcpConnector();

      // 禁用拷贝构造和赋值
      TcpConnector(const TcpConnector&) = delete;
      TcpConnector& operator=(const TcpConnector&) = delete;

      void setNewConnectionCallback(const NewConnectionCallback& cb) {
        newConnectionCallback_ = cb;
      }

      void start();    // can be called in any thread
      void restart();  // must be called in loop thread
      void stop();     // can be called in any thread

      const zexuan::event::Address& serverAddress() const { return serverAddr_; }

    private:
      enum States { kDisconnected, kConnecting, kConnected };
      static const int kMaxRetryDelayMs = 30 * 1000;
      static const int kInitRetryDelayMs = 500;

      void setState(States s) { state_ = s; }
      void startInLoop();
      void stopInLoop();
      void connect();
      void connecting(int sockfd);
      void handleWrite();
      void handleError();
      void retry(int sockfd);
      int removeAndResetChannel();
      void resetChannel();

      EventLoop* loop_;
      zexuan::event::Address serverAddr_;
      bool connect_;  // atomic
      States state_;  // FIXME: use atomic variable
      std::unique_ptr<Channel> channel_;
      NewConnectionCallback newConnectionCallback_;
      int retryDelayMs_;
      TimerId retryTimerId_;
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_TCP_CONNECTOR_HPP
