#ifndef ZEXUAN_EVENT_UDS_CONNECTOR_HPP
#define ZEXUAN_EVENT_UDS_CONNECTOR_HPP

#include <functional>
#include <memory>
#include <string>

#include "zexuan/event/callbacks.hpp"
#include "zexuan/event/channel.hpp"

namespace zexuan {
  namespace event {

    class EventLoop;

    class UdsConnector : public std::enable_shared_from_this<UdsConnector> {
    public:
      typedef std::function<void(int sockfd)> NewConnectionCallback;

      UdsConnector(EventLoop* loop, const std::string& serverPath);
      ~UdsConnector();

      // 禁用拷贝构造和赋值
      UdsConnector(const UdsConnector&) = delete;
      UdsConnector& operator=(const UdsConnector&) = delete;

      void setNewConnectionCallback(const NewConnectionCallback& cb) {
        newConnectionCallback_ = cb;
      }

      void start();    // can be called in any thread
      void restart();  // must be called in loop thread
      void stop();     // can be called in any thread

      const std::string& serverPath() const { return serverPath_; }

    private:
      enum States { kDisconnected, kConnecting, kConnected };
      static const int kMaxRetryDelayMs = 30 * 1000;
      static const int kInitRetryDelayMs = 500;

      void setState(States s) { state_ = s; }
      void startInLoop();
      void stopInLoop();
      void connect();
      void connecting(int sockfd);
      void handleWrite();
      void handleError();
      void retry(int sockfd);
      int removeAndResetChannel();
      void resetChannel();

      EventLoop* loop_;
      std::string serverPath_;
      bool connect_;  // atomic
      States state_;  // FIXME: use atomic variable
      std::unique_ptr<Channel> channel_;
      NewConnectionCallback newConnectionCallback_;
      int retryDelayMs_;
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_UDS_CONNECTOR_HPP
