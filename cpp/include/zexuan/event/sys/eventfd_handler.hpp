#ifndef ZEXUAN_EVENT_EVENTFD_HANDLER_HPP
#define ZEXUAN_EVENT_EVENTFD_HANDLER_HPP

#include <atomic>
#include <functional>
#include <memory>

#include "zexuan/event/callbacks.hpp"

namespace zexuan {
  namespace event {

    class EventLoop;
    class Channel;

    /**
     * @brief EventfdHandler 封装 eventfd 系统调用，提供线程间事件通知功能
     *
     * 使用 eventfd 实现线程间通信，支持计数器语义和非阻塞操作
     * 线程安全，可以从任意线程调用 notify()
     * 采用与 TimerQueue 和 SignalHandler 相同的架构模式
     */
    class EventfdHandler {
    public:
      using NotifyCallback = std::function<void()>;

      /**
       * @brief 构造函数
       * @param loop 事件循环指针
       */
      explicit EventfdHandler(EventLoop* loop);

      /**
       * @brief 析构函数
       */
      ~EventfdHandler();

      // 禁用拷贝构造和赋值
      EventfdHandler(const EventfdHandler&) = delete;
      EventfdHandler& operator=(const EventfdHandler&) = delete;

      /**
       * @brief 设置通知回调函数
       * @param cb 回调函数
       *
       * 线程安全，可以从任意线程调用
       */
      void setNotifyCallback(NotifyCallback cb);

      /**
       * @brief 发送通知
       * @param count 通知计数，默认为1
       *
       * 线程安全，可以从任意线程调用
       * 支持 eventfd 的计数器语义
       */
      void notify(uint64_t count = 1);

      /**
       * @brief 获取当前计数值（非阻塞）
       * @return 当前计数值，如果没有事件则返回0
       *
       * 只能在事件循环线程中调用
       */
      uint64_t getCount();

      /**
       * @brief 重置计数器
       *
       * 只能在事件循环线程中调用
       */
      void reset();

    private:
      /**
       * @brief 处理 eventfd 可读事件
       */
      void handleRead();

      /**
       * @brief 在事件循环中设置回调函数
       */
      void setNotifyCallbackInLoop(NotifyCallback cb);

      EventLoop* loop_;
      const int eventfd_;
      std::unique_ptr<Channel> eventfdChannel_;
      NotifyCallback notifyCallback_;
      std::atomic<bool> callbackSet_;
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_EVENTFD_HANDLER_HPP
