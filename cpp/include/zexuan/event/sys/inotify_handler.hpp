#ifndef ZEXUAN_EVENT_INOTIFY_HANDLER_HPP
#define ZEXUAN_EVENT_INOTIFY_HANDLER_HPP

#include <sys/inotify.h>

#include <atomic>
#include <cstdint>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "zexuan/event/callbacks.hpp"

namespace zexuan {
  namespace event {

    class EventLoop;
    class Channel;

    // 前向声明
    class InotifyHandler;

    /**
     * @brief 监听ID，用于标识和取消文件监听
     * 采用与TimerId相同的设计模式
     */
    class WatchId {
    public:
      /**
       * @brief 默认构造函数
       */
      WatchId() : watch_(nullptr), sequence_(0) {}

      /**
       * @brief 构造函数
       * @param watch 监听对象指针
       * @param seq 序列号
       */
      WatchId(void* watch, int64_t seq) : watch_(watch), sequence_(seq) {}

      /**
       * @brief 检查WatchId是否有效
       */
      bool valid() const { return watch_ != nullptr; }

      friend class InotifyHandler;

    private:
      void* watch_;  // 使用void*避免前向声明问题
      int64_t sequence_;
    };

    /**
     * @brief InotifyHandler 封装 inotify 系统调用，提供文件系统监听功能
     *
     * 使用 inotify 实现文件和目录监听，支持多种事件类型
     * 线程安全，可以从任意线程调用监听接口
     * 采用与TimerHandler相同的架构模式
     */
    class InotifyHandler {
    public:
      /**
       * @brief 文件系统事件回调函数类型
       * @param path 发生事件的文件路径
       * @param mask 事件掩码
       * @param name 文件名（对于目录事件）
       */
      using FileEventCallback
          = std::function<void(const std::string& path, uint32_t mask, const std::string& name)>;

      /**
       * @brief 构造函数
       * @param loop 事件循环指针
       */
      explicit InotifyHandler(EventLoop* loop);

      /**
       * @brief 析构函数
       */
      ~InotifyHandler();

      // 禁用拷贝构造和赋值
      InotifyHandler(const InotifyHandler&) = delete;
      InotifyHandler& operator=(const InotifyHandler&) = delete;

      /**
       * @brief 监听文件或目录
       * @param path 要监听的文件或目录路径
       * @param mask 事件掩码（IN_CREATE, IN_DELETE, IN_MODIFY 等）
       * @param cb 事件回调函数
       * @return 监听ID，用于取消监听
       *
       * 线程安全，可以从任意线程调用
       */
      WatchId addWatch(const std::string& path, uint32_t mask, FileEventCallback cb);

      /**
       * @brief 监听文件修改事件（便捷接口）
       * @param path 文件路径
       * @param cb 事件回调函数
       * @return 监听ID
       *
       * 线程安全，可以从任意线程调用
       */
      WatchId watchFile(const std::string& path, FileEventCallback cb);

      /**
       * @brief 监听目录事件（便捷接口）
       * @param path 目录路径
       * @param cb 事件回调函数
       * @return 监听ID
       *
       * 线程安全，可以从任意线程调用
       */
      WatchId watchDirectory(const std::string& path, FileEventCallback cb);

      /**
       * @brief 取消监听
       * @param watchId 监听ID
       *
       * 线程安全，可以从任意线程调用
       */
      void removeWatch(WatchId watchId);

      /**
       * @brief 取消所有监听
       *
       * 线程安全，可以从任意线程调用
       */
      void unwatchAll();

      /**
       * @brief 获取事件掩码的字符串描述
       * @param mask 事件掩码
       * @return 事件描述字符串
       */
      static std::string maskToString(uint32_t mask);

    private:
      /**
       * @brief 内部监听对象
       */
      class InotifyWatch {
      public:
        InotifyWatch(const std::string& path, uint32_t mask, FileEventCallback cb, int wd)
            : path_(path),
              mask_(mask),
              callback_(std::move(cb)),
              wd_(wd),
              sequence_(s_numCreated_.fetch_add(1, std::memory_order_relaxed)) {}

        void run(uint32_t eventMask, const std::string& name) const {
          callback_(path_, eventMask, name);
        }

        const std::string& path() const { return path_; }
        uint32_t mask() const { return mask_; }
        int wd() const { return wd_; }
        int64_t sequence() const { return sequence_; }

        static int64_t numCreated() { return s_numCreated_.load(std::memory_order_relaxed); }

      private:
        std::string path_;
        uint32_t mask_;
        FileEventCallback callback_;
        int wd_;
        int64_t sequence_;
        static std::atomic<int64_t> s_numCreated_;
      };

      using WatchPtr = std::unique_ptr<InotifyWatch>;

      /**
       * @brief 处理 inotify 可读事件
       */
      void handleRead();

      /**
       * @brief 在事件循环中添加监听
       */
      WatchId addWatchInLoop(const std::string& path, uint32_t mask, FileEventCallback cb);

      /**
       * @brief 在事件循环中取消监听
       */
      void removeWatchInLoop(WatchId watchId);

      /**
       * @brief 在事件循环中取消所有监听
       */
      void removeAllWatchesInLoop();

      EventLoop* loop_;
      const int inotifyfd_;
      std::unique_ptr<Channel> inotifyChannel_;
      std::map<int, InotifyWatch*> watches_;  // wd -> InotifyWatch*
      std::vector<WatchPtr> watchStorage_;    // 存储所有监听对象
    };

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_INOTIFY_HANDLER_HPP
