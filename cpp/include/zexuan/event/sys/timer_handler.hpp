/**
 * @file timer_handler.hpp
 * @brief 统一的定时器处理器，包含Timer、TimerId和TimerHandler类
 * <AUTHOR>
 * @date 2025-08-27
 */

#ifndef ZEXUAN_EVENT_TIMER_HANDLER_HPP
#define ZEXUAN_EVENT_TIMER_HANDLER_HPP

#include <atomic>
#include <chrono>
#include <cstdint>
#include <functional>
#include <memory>
#include <set>
#include <vector>

#include "zexuan/event/callbacks.hpp"

namespace zexuan {
  namespace event {

    class EventLoop;
    class Channel;

    // 前向声明
    class Timer;

    /**
     * @brief 定时器ID，用于标识和取消定时器
     */
    class TimerId {
    public:
      /**
       * @brief 默认构造函数
       */
      TimerId() : timer_(nullptr), sequence_(0) {}

      /**
       * @brief 构造函数
       * @param timer 定时器指针
       * @param seq 序列号
       */
      TimerId(Timer* timer, int64_t seq) : timer_(timer), sequence_(seq) {}

      // 默认拷贝构造、析构和赋值操作符

      /**
       * @brief 检查TimerId是否有效
       */
      bool valid() const { return timer_ != nullptr; }

      friend class TimerHandler;

    private:
      Timer* timer_;
      int64_t sequence_;
    };

    /**
     * @brief 定时器类，用于管理单个定时器事件
     */
    class Timer {
    public:
      using TimerCallback = std::function<void()>;
      using Timestamp = std::chrono::system_clock::time_point;

      /**
       * @brief 构造函数
       * @param cb 定时器回调函数
       * @param when 到期时间
       * @param interval 重复间隔（秒），0表示不重复
       */
      Timer(TimerCallback cb, Timestamp when, double interval)
          : callback_(std::move(cb)),
            expiration_(when),
            interval_(std::chrono::duration<double>(interval)),
            repeat_(interval > 0.0),
            sequence_(s_numCreated_.fetch_add(1, std::memory_order_relaxed)) {}

      // 禁用拷贝构造和赋值
      Timer(const Timer&) = delete;
      Timer& operator=(const Timer&) = delete;

      /**
       * @brief 执行定时器回调
       */
      void run() const { callback_(); }

      /**
       * @brief 获取到期时间
       */
      Timestamp expiration() const { return expiration_; }

      /**
       * @brief 是否重复执行
       */
      bool repeat() const { return repeat_; }

      /**
       * @brief 获取序列号
       */
      int64_t sequence() const { return sequence_; }

      /**
       * @brief 重启定时器（用于重复定时器）
       * @param now 当前时间
       */
      void restart(Timestamp now);

      /**
       * @brief 获取已创建的定时器数量
       */
      static int64_t numCreated() { return s_numCreated_.load(std::memory_order_relaxed); }

    private:
      const TimerCallback callback_;
      Timestamp expiration_;
      const std::chrono::duration<double> interval_;
      const bool repeat_;
      const int64_t sequence_;

      static std::atomic<int64_t> s_numCreated_;
    };

    /**
     * @brief 定时器处理器，管理所有定时器
     *
     * 使用 timerfd 实现，所有定时器事件都在事件循环中处理
     * 线程安全，可以从任意线程添加/取消定时器
     * 采用与其他Handler相同的架构模式
     */
    class TimerHandler {
    public:
      /**
       * @brief 构造函数
       * @param loop 事件循环指针
       */
      explicit TimerHandler(EventLoop* loop);

      /**
       * @brief 析构函数
       */
      ~TimerHandler();

      // 禁用拷贝构造和赋值
      TimerHandler(const TimerHandler&) = delete;
      TimerHandler& operator=(const TimerHandler&) = delete;

      /**
       * @brief 添加定时器
       * @param cb 回调函数
       * @param when 到期时间
       * @param interval 重复间隔（秒），0表示不重复
       * @return TimerId 用于取消定时器
       *
       * 线程安全，可以从任意线程调用
       */
      TimerId addTimer(TimerCallback cb, Timestamp when, double interval);

      /**
       * @brief 取消定时器
       * @param timerId 定时器ID
       *
       * 线程安全，可以从任意线程调用
       */
      void cancel(TimerId timerId);

      // 便捷接口
      TimerId runAt(Timestamp when, TimerCallback cb);
      TimerId runAfter(double delay, TimerCallback cb);
      TimerId runEvery(double interval, TimerCallback cb);

    private:
      // 使用 unique_ptr 管理 Timer，避免内存泄漏
      using TimerPtr = std::unique_ptr<Timer>;
      using Entry = std::pair<Timestamp, Timer*>;
      using TimerList = std::set<Entry>;
      using ActiveTimer = std::pair<Timer*, int64_t>;
      using ActiveTimerSet = std::set<ActiveTimer>;

      /**
       * @brief 在事件循环中添加定时器
       */
      void addTimerInLoop(Timer* timer);

      /**
       * @brief 在事件循环中取消定时器
       */
      void cancelInLoop(TimerId timerId);

      /**
       * @brief 处理 timerfd 可读事件
       */
      void handleRead();

      /**
       * @brief 获取所有过期的定时器
       */
      std::vector<Entry> getExpired(Timestamp now);

      /**
       * @brief 重置过期的定时器（处理重复定时器）
       */
      void reset(const std::vector<Entry>& expired, Timestamp now);

      /**
       * @brief 插入定时器到队列中
       * @return 是否改变了最早的到期时间
       */
      bool insert(Timer* timer);

      EventLoop* loop_;
      const int timerfd_;
      std::unique_ptr<Channel> timerfdChannel_;

      // 按到期时间排序的定时器列表
      TimerList timers_;

      // 用于取消操作的活跃定时器集合
      ActiveTimerSet activeTimers_;

      // 正在处理过期定时器的标志
      bool callingExpiredTimers_;

      // 正在取消的定时器集合
      ActiveTimerSet cancelingTimers_;

      // 存储所有定时器的智能指针，确保内存管理
      std::vector<TimerPtr> timerStorage_;
    };

    // 类型别名，保持向后兼容
    using TimerQueue = TimerHandler;

  }  // namespace event
}  // namespace zexuan

#endif  // ZEXUAN_EVENT_TIMER_HANDLER_HPP
