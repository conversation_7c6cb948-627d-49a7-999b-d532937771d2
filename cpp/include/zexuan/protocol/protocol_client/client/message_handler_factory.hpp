/**
 * @file message_handler_factory.hpp
 * @brief 简化的消息处理器工厂
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_FACTORY_HPP
#define ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_FACTORY_HPP

#include <memory>
#include <mutex>
#include <unordered_map>

#include "message_handler_interface.hpp"

namespace zexuan {
  namespace protocol {
    namespace client {

      /**
       * @brief 消息类型处理器工厂实现
       */
      class MessageTypeHandlerFactory {
      public:
        /**
         * @brief 构造函数
         */
        MessageTypeHandlerFactory();

        /**
         * @brief 析构函数
         */
        ~MessageTypeHandlerFactory() = default;

        /**
         * @brief 获取指定类型的处理器
         * @param message_type 消息类型
         * @return 处理器指针，如果不支持则返回nullptr
         */
        IMessageTypeHandler* GetHandler(uint8_t message_type);

      private:
        mutable std::mutex mutex_;
        std::unordered_map<uint8_t, std::unique_ptr<IMessageTypeHandler>> handlers_;

        /**
         * @brief 初始化默认handlers
         */
        void InitializeDefaultHandlers();
      };

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

#endif  // ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_FACTORY_HPP
