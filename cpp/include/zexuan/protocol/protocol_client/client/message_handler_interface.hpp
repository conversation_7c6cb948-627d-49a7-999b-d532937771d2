/**
 * @file message_handler_interface.hpp
 * @brief 简化的消息处理器接口
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_INTERFACE_HPP
#define ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_INTERFACE_HPP

#include <memory>
#include <string>
#include <vector>

#include "zexuan/base/base_types.hpp"
#include "zexuan/base/message.hpp"

namespace zexuan {
  namespace protocol {
    namespace client {

      // 直接使用base::Message，无需额外封装

      /**
       * @brief 消息处理结果
       */
      struct MessageResult {
        bool success = false;
        std::string description;
        std::vector<uint8_t> data;
      };

      /**
       * @brief 消息类型处理器接口
       */
      class IMessageTypeHandler {
      public:
        virtual ~IMessageTypeHandler() = default;

        /**
         * @brief 验证消息参数
         * @param message 消息对象（包含所有参数，包括类型）
         * @return 验证结果
         */
        virtual bool ValidateRequest(const base::Message& message) const = 0;

        /**
         * @brief 创建消息
         * @param message 输入消息对象（包含参数），输出完整消息
         * @return 创建的消息
         */
        virtual base::Result<base::Message> CreateMessage(const base::Message& message) const = 0;

        /**
         * @brief 处理响应消息
         * @param response 响应消息
         * @return 处理结果
         */
        virtual base::Result<MessageResult> ProcessResponse(const base::Message& response) const
            = 0;
      };

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

#endif  // ZEXUAN_PROTOCOL_CLIENT_MESSAGE_HANDLER_INTERFACE_HPP
