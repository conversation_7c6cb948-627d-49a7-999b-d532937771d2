/**
 * @file protocol_client.hpp
 * @brief 简化的IEC 60870-5-103协议客户端
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HPP

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <string>

#include "zexuan/base/base_types.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/tcp_client.hpp"

// 前向声明
namespace zexuan {
  namespace protocol {
    namespace client {
      class IMessageTypeHandler;
      class MessageTypeHandlerFactory;
      struct MessageRequest;
    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

namespace zexuan {
  namespace protocol {
    namespace client {

      /**
       * @brief 简化的协议客户端
       *
       * 保留handlers路由机制的简化实现：
       * 1. TCP连接管理（集成TcpConnectionManager功能）
       * 2. 消息发送（通过handlers factory路由）
       * 3. 消息接收与解析（集成MessageFrameParser功能）
       * 4. 回调通知
       * 5. 自动handler路由
       */
      class ProtocolClient {
      public:
        // 回调函数类型定义
        using ConnectionCallback = std::function<void(bool connected)>;
        using MessageCallback = std::function<void(const base::Message& message)>;

        /**
         * @brief 构造函数
         * @param event_loop 事件循环指针
         * @param config_file_path 配置文件路径
         */
        ProtocolClient(event::EventLoop* event_loop, const std::string& config_file_path);

        /**
         * @brief 析构函数
         */
        ~ProtocolClient();

        // 禁止拷贝和移动
        ProtocolClient(const ProtocolClient&) = delete;
        ProtocolClient& operator=(const ProtocolClient&) = delete;
        ProtocolClient(ProtocolClient&&) = delete;
        ProtocolClient& operator=(ProtocolClient&&) = delete;

        /**
         * @brief 启动客户端
         * @return 启动结果
         */
        base::Result<void> Start();

        /**
         * @brief 停止客户端
         * @return 停止结果
         */
        base::Result<void> Stop();

        /**
         * @brief 连接到服务器
         * @return 连接结果
         */
        base::Result<void> Connect();

        /**
         * @brief 断开连接
         */
        void Disconnect();

        /**
         * @brief 检查是否已连接
         * @return true: 已连接, false: 未连接
         */
        bool IsConnected() const;

        /**
         * @brief 发送Type1消息（单帧测试）
         * @param target 目标地址
         * @param test_data 测试数据（可选）
         * @return 发送结果
         */
        base::Result<void> SendType1Message(uint8_t target, const std::string& test_data = "");

        /**
         * @brief 发送Type2消息（多帧测试）
         * @param target 目标地址
         * @param test_data 测试数据（可选）
         * @return 发送结果
         */
        base::Result<void> SendType2Message(uint8_t target, const std::string& test_data = "");

        /**
         * @brief 发送Type3消息（时间戳事件）
         * @param target 目标地址
         * @return 发送结果
         */
        base::Result<void> SendType3Message(uint8_t target);

        /**
         * @brief 发送Type4消息（本地状态查询）
         * @param target 目标地址
         * @return 发送结果
         */
        base::Result<void> SendType4Message(uint8_t target);

        /**
         * @brief 发送Type5消息（重命名请求）
         * @param target 目标地址
         * @param new_name 新名称
         * @return 发送结果
         */
        base::Result<void> SendType5Message(uint8_t target, const std::string& new_name);

        /**
         * @brief 设置连接状态回调
         * @param callback 回调函数
         */
        void SetConnectionCallback(ConnectionCallback callback);

        /**
         * @brief 设置消息接收回调
         * @param callback 回调函数
         */
        void SetMessageCallback(MessageCallback callback);

      /**
       * @brief 获取消息的文本内容（根据消息类型专门解析）
       * @param message 消息对象
       * @return 解析后的文本内容
       */
      std::string GetMessageTextContent(const base::Message& message);

      private:
        // 配置信息
        std::string config_file_path_;
        std::string server_host_;
        uint16_t server_port_;

        // 网络组件
        event::EventLoop* event_loop_;
        std::unique_ptr<event::TcpClient> tcp_client_;
        event::TcpConnectionPtr connection_;

        // 状态管理
        std::atomic<bool> is_running_{false};
        std::atomic<bool> is_connected_{false};

        // 回调函数
        std::mutex callback_mutex_;
        ConnectionCallback connection_callback_;
        MessageCallback message_callback_;

        // 消息处理缓冲区
        std::string receive_buffer_;
        std::mutex buffer_mutex_;

        // 消息处理器工厂（保留handlers路由机制）
        std::unique_ptr<MessageTypeHandlerFactory> handler_factory_;

        // 多帧响应缓存（用于Type2等多帧消息）
        struct MultiFrameResponse {
          std::vector<base::Message> frames;
          std::chrono::system_clock::time_point first_frame_time;

          MultiFrameResponse() : first_frame_time(std::chrono::system_clock::now()) {}
        };
        std::map<std::string, MultiFrameResponse> multi_frame_cache_;
        std::mutex multi_frame_cache_mutex_;
        static constexpr std::chrono::seconds MULTI_FRAME_TIMEOUT{5}; // 多帧响应超时时间

        // 私有方法
        void OnConnection(const event::TcpConnectionPtr& conn);
        void OnMessage(const event::TcpConnectionPtr& conn, event::Buffer* buf,
                       std::chrono::system_clock::time_point receive_time);
        void OnWriteComplete(const event::TcpConnectionPtr& conn);

        // 消息处理
        void ProcessReceivedData(const std::string& data);
        bool ParseMessage(const std::vector<uint8_t>& frame_data, base::Message& message);
        void ProcessParsedMessage(const base::Message& message);
        void CleanupExpiredMultiFrameCache();
        
        // 消息文本内容解析（根据消息类型提供专门的解析方法）
        std::string ParseType3MessageContent(const std::vector<uint8_t>& variable_data);
        std::string ParseType4MessageContent(const std::vector<uint8_t>& variable_data);
        std::string ExtractPrintableText(const std::vector<uint8_t>& data);

        // 消息发送辅助方法（通过handlers路由）
        base::Result<void> SendMessageViaHandler(uint8_t message_type, uint8_t target,
                                                 const std::string& text_content = "",
                                                 const std::vector<uint8_t>& variable_data = {});

        // 配置管理
        bool LoadConfiguration();

        // Handlers管理
        void InitializeHandlers();
        IMessageTypeHandler* GetHandler(uint8_t message_type);

        // 回调通知
        void NotifyConnectionStatus(bool connected);
        void NotifyMessageReceived(const base::Message& message);
      };

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

#endif  // ZEXUAN_PROTOCOL_CLIENT_HPP
