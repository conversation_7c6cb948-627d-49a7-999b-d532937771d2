/**
 * @file type2_handler.hpp
 * @brief Type 2 多帧测试消息处理器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE2_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE2_HANDLER_HPP

#include "zexuan/protocol/protocol_client/client/message_handler_interface.hpp"

namespace zexuan {
  namespace protocol {
    namespace client {

      /**
       * @brief Type 2 多帧测试消息处理器
       *
       * 处理IEC 60870-5-103协议中的Type 2（多帧测试）消息
       * 对应原始ChatClient中的sendMultiFrameTest功能
       */
      class Type2Handler : public IMessageTypeHandler {
      public:
        /**
         * @brief 构造函数
         */
        Type2Handler();

        /**
         * @brief 析构函数
         */
        ~Type2Handler() override = default;

        // 实现IMessageTypeHandler接口
        bool ValidateRequest(const base::Message& message) const override;
        base::Result<base::Message> CreateMessage(const base::Message& message) const override;
        base::Result<MessageResult> ProcessResponse(const base::Message& response) const override;

      private:
        // Type 2 消息的默认参数
        static constexpr uint8_t DEFAULT_VSQ = 0x81;  ///< 默认可变结构限定词（多信息）
        static constexpr uint8_t DEFAULT_COT = 0x06;  ///< 默认传送原因（激活）
        static constexpr uint8_t DEFAULT_FUN = 0xFF;  ///< 默认功能类型
        static constexpr uint8_t DEFAULT_INF = 0x13;  ///< 默认信息序号

        // 默认测试数据
        static const std::string DEFAULT_TEST_DATA;
      };

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

#endif  // ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE2_HANDLER_HPP
