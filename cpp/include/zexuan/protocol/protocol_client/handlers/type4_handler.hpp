/**
 * @file type4_handler.hpp
 * @brief Type 4 本地状态消息处理器
 * <AUTHOR>
 * @date 2024
 */

#ifndef ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE4_HANDLER_HPP
#define ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE4_HANDLER_HPP

#include "zexuan/protocol/protocol_client/client/message_handler_interface.hpp"

namespace zexuan {
  namespace protocol {
    namespace client {

      /**
       * @brief Type 4 本地状态消息处理器
       *
       * 处理IEC 60870-5-103协议中的Type 4（本地状态）消息
       * 对应原始ChatClient中的sendType4LocalStatusRequest和parseType4LocalStatusResponse功能
       */
      class Type4Handler : public IMessageTypeHandler {
      public:
        /**
         * @brief 构造函数
         */
        Type4Handler();

        /**
         * @brief 析构函数
         */
        ~Type4Handler() override = default;

      public:
        // 实现IMessageTypeHandler接口
        bool ValidateRequest(const base::Message& message) const override;
        base::Result<base::Message> CreateMessage(const base::Message& message) const override;
        base::Result<MessageResult> ProcessResponse(const base::Message& response) const override;

      private:
        // Type 4 消息的默认参数
        static constexpr uint8_t DEFAULT_VSQ = 0x01;  ///< 默认可变结构限定词（单信息元素）
        static constexpr uint8_t DEFAULT_COT = 0x06;  ///< 默认传送原因（激活）
        static constexpr uint8_t DEFAULT_FUN = 0xF1;  ///< 默认功能类型（本地状态查询）
        static constexpr uint8_t DEFAULT_INF = 0x01;  ///< 默认信息序号（状态信息）

        // 状态数据结构常量
        static constexpr size_t IEC103_TIMESTAMP_SIZE = 7;  ///< IEC103时间戳大小
        static constexpr size_t MIN_STATUS_DATA_SIZE
            = 11;  ///< 最小状态数据大小（7字节时间戳+4字节状态）
        static constexpr size_t FULL_STATUS_DATA_SIZE = 15;  ///< 完整状态数据大小（包含负载信息）

        /**
         * @brief 创建默认的状态请求数据
         * @return 请求数据
         */
        std::vector<uint8_t> CreateDefaultRequestData() const;

        /**
         * @brief 解析状态响应数据
         * @param status_data 状态数据
         * @return 解析后的状态描述
         */
        std::string ParseStatusResponse(const std::vector<uint8_t>& status_data) const;

        /**
         * @brief 解析设备状态字节
         * @param status_byte 状态字节
         * @return 状态描述
         */
        std::string ParseDeviceStatus(uint8_t status_byte) const;

        /**
         * @brief 解析通信状态字节
         * @param status_byte 状态字节
         * @return 状态描述
         */
        std::string ParseCommStatus(uint8_t status_byte) const;

        /**
         * @brief 解析运行模式字节
         * @param mode_byte 模式字节
         * @return 模式描述
         */
        std::string ParseRunMode(uint8_t mode_byte) const;

        /**
         * @brief 解析报警状态字节
         * @param alarm_byte 报警字节
         * @return 报警状态描述
         */
        std::string ParseAlarmStatus(uint8_t alarm_byte) const;

        /**
         * @brief 格式化时间戳（从Type3Handler借用逻辑）
         * @param timestamp_data 时间戳数据
         * @return 格式化的时间字符串
         */
        std::string FormatTimestamp(const std::vector<uint8_t>& timestamp_data) const;
      };

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan

#endif  // ZEXUAN_PROTOCOL_CLIENT_HANDLERS_TYPE4_HANDLER_HPP
