/**
 * @file message_protocol.hpp
 * @brief TCP消息协议处理 - 定义消息长度+JSON内容的传输协议
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_UTILS_MESSAGE_PROTOCOL_HPP
#define ZEXUAN_UTILS_MESSAGE_PROTOCOL_HPP

#include <cstdint>
#include <optional>
#include <string>
#include <vector>

namespace zexuan {
  namespace utils {

    /**
     * @brief TCP消息协议处理类
     *
     * 协议格式：[4字节消息长度(网络字节序)] + [JSON消息内容]
     * - 消息长度：uint32_t，网络字节序，表示后续JSON内容的字节数
     * - JSON内容：UTF-8编码的JSON字符串
     */
    class MessageProtocol {
    public:
      /**
       * @brief 协议常量
       */
      static constexpr size_t HEADER_SIZE = 4;                 ///< 消息头大小（4字节长度）
      static constexpr size_t MAX_MESSAGE_SIZE = 1024 * 1024;  ///< 最大消息大小（1MB）

      /**
       * @brief 打包消息
       * @param json_content JSON消息内容
       * @return 打包后的二进制数据，失败返回空vector
       */
      static std::vector<uint8_t> packMessage(const std::string& json_content);

      /**
       * @brief 解包消息头
       * @param header_data 4字节的消息头数据
       * @return 消息体长度，失败返回nullopt
       */
      static std::optional<uint32_t> unpackHeader(const std::vector<uint8_t>& header_data);

      /**
       * @brief 解包消息体
       * @param body_data 消息体数据
       * @param expected_length 期望的消息体长度
       * @return JSON消息内容，失败返回nullopt
       */
      static std::optional<std::string> unpackBody(const std::vector<uint8_t>& body_data,
                                                   uint32_t expected_length);

      /**
       * @brief 验证消息完整性
       * @param packed_message 完整的打包消息
       * @return 是否完整有效
       */
      static bool validateMessage(const std::vector<uint8_t>& packed_message);

      /**
       * @brief 从完整消息中提取JSON内容
       * @param packed_message 完整的打包消息
       * @return JSON消息内容，失败返回nullopt
       */
      static std::optional<std::string> extractJsonFromPackedMessage(
          const std::vector<uint8_t>& packed_message);

    private:
      /**
       * @brief 主机字节序转网络字节序
       * @param host_value 主机字节序的值
       * @return 网络字节序的值
       */
      static uint32_t hostToNetwork(uint32_t host_value);

      /**
       * @brief 网络字节序转主机字节序
       * @param network_value 网络字节序的值
       * @return 主机字节序的值
       */
      static uint32_t networkToHost(uint32_t network_value);

      /**
       * @brief 验证JSON格式
       * @param json_str JSON字符串
       * @return 是否为有效JSON
       */
      static bool isValidJson(const std::string& json_str);
    };

    /**
     * @brief 消息缓冲区类
     *
     * 用于处理TCP流式数据的消息边界问题
     */
    class MessageBuffer {
    public:
      MessageBuffer() = default;

      /**
       * @brief 添加接收到的数据
       * @param data 新接收的数据
       */
      void appendData(const std::vector<uint8_t>& data);

      /**
       * @brief 添加接收到的数据
       * @param data 新接收的数据指针
       * @param size 数据大小
       */
      void appendData(const uint8_t* data, size_t size);

      /**
       * @brief 尝试提取完整消息
       * @return 完整的JSON消息，如果没有完整消息返回nullopt
       */
      std::optional<std::string> extractMessage();

      /**
       * @brief 获取缓冲区大小
       * @return 缓冲区中的数据大小
       */
      size_t size() const { return buffer_.size(); }

      /**
       * @brief 清空缓冲区
       */
      void clear() { buffer_.clear(); }

      /**
       * @brief 检查是否有足够数据构成完整消息
       * @return 是否有完整消息
       */
      bool hasCompleteMessage() const;

    private:
      std::vector<uint8_t> buffer_;  ///< 数据缓冲区

      /**
       * @brief 从缓冲区中移除指定长度的数据
       * @param length 要移除的数据长度
       */
      void removeData(size_t length);
    };

  }  // namespace utils
}  // namespace zexuan

#endif  // ZEXUAN_UTILS_MESSAGE_PROTOCOL_HPP
