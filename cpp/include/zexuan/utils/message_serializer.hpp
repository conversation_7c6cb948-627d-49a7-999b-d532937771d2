/**
 * @file message_serializer.hpp
 * @brief JSON消息序列化器 - 处理CommonMessage和EventMessage的JSON转换
 * <AUTHOR>
 * @date 2025-08-24
 */

#ifndef ZEXUAN_UTILS_MESSAGE_SERIALIZER_HPP
#define ZEXUAN_UTILS_MESSAGE_SERIALIZER_HPP

#include <optional>
#include <string>
#include <vector>

#include "nlohmann/json.hpp"
#include "zexuan/base/types/structs.hpp"

namespace zexuan {
  namespace utils {

    /**
     * @brief JSON消息序列化器类
     *
     * 负责CommonMessage、EventMessage、订阅消息等与JSON的相互转换
     */
    class MessageSerializer {
    public:
      /**
       * @brief CommonMessage转JSON
       * @param msg CommonMessage对象
       * @return JSON字符串
       */
      static std::string serializeCommonMessage(const zexuan::base::CommonMessage& msg);

      /**
       * @brief JSON转CommonMessage
       * @param json_str JSON字符串
       * @return CommonMessage对象，失败返回nullopt
       */
      static std::optional<zexuan::base::CommonMessage> deserializeCommonMessage(
          const std::string& json_str);

      /**
       * @brief EventMessage转JSON
       * @param msg EventMessage对象
       * @return JSON字符串
       */
      static std::string serializeEventMessage(const zexuan::base::EventMessage& msg);

      /**
       * @brief JSON转EventMessage
       * @param json_str JSON字符串
       * @return EventMessage对象，失败返回nullopt
       */
      static std::optional<zexuan::base::EventMessage> deserializeEventMessage(
          const std::string& json_str);

      /**
       * @brief 订阅消息转JSON
       * @param msg 订阅消息对象
       * @return JSON字符串
       */
      static std::string serializeSubscriptionMessage(const zexuan::base::SubscriptionMessage& msg);

      /**
       * @brief JSON转订阅消息
       * @param json_str JSON字符串
       * @return 订阅消息对象，失败返回nullopt
       */
      static std::optional<zexuan::base::SubscriptionMessage> deserializeSubscriptionMessage(
          const std::string& json_str);

      /**
       * @brief 控制消息转JSON
       * @param msg 控制消息对象
       * @return JSON字符串
       */
      static std::string serializeControlMessage(const zexuan::base::ControlMessage& msg);

      /**
       * @brief JSON转控制消息
       * @param json_str JSON字符串
       * @return 控制消息对象，失败返回nullopt
       */
      static std::optional<zexuan::base::ControlMessage> deserializeControlMessage(
          const std::string& json_str);

      /**
       * @brief Base64编码
       * @param data 二进制数据
       * @return Base64编码字符串
       */
      static std::string encodeBase64(const std::vector<uint8_t>& data);

      /**
       * @brief Base64解码
       * @param encoded Base64编码字符串
       * @return 二进制数据，失败返回空vector
       */
      static std::vector<uint8_t> decodeBase64(const std::string& encoded);

    private:
      /**
       * @brief 安全的JSON解析
       * @param json_str JSON字符串
       * @return JSON对象，失败返回nullopt
       */
      static std::optional<nlohmann::json> safeParseJson(const std::string& json_str);

      /**
       * @brief 验证JSON是否包含必需字段
       * @param json JSON对象
       * @param required_fields 必需字段列表
       * @return 是否包含所有必需字段
       */
      static bool validateRequiredFields(const nlohmann::json& json,
                                         const std::vector<std::string>& required_fields);
    };

  }  // namespace utils
}  // namespace zexuan

#endif  // ZEXUAN_UTILS_MESSAGE_SERIALIZER_HPP
