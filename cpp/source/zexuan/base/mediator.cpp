#include "zexuan/base/mediator.hpp"

#include <algorithm>
#include <format>
#include <iostream>
#include <ranges>

#include "zexuan/base/controller.hpp"
#include "zexuan/base/executor.hpp"

namespace zexuan {
  namespace base {

    Mediator::Mediator() {}

    Mediator::~Mediator() noexcept {
      try {
        ClearAll();
      } catch (...) {
        // 析构函数中不应该抛出异常
      }
    }

    VoidResult Mediator::EnrollController(ObjectId controller_id,
                                          std::shared_ptr<Controller> controller) {
      if (!controller) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      std::unique_lock<std::shared_mutex> lock(controller_mutex_);

      // 检查是否已经注册
      if (controller_map_.contains(controller_id)) {
        return std::unexpected(ErrorCode::ALREADY_EXISTS);
      }

      // 注册控制器
      controller_map_[controller_id] = std::move(controller);

      std::cout << std::format("EnrollController() 控制器ID {} 注册成功\n", controller_id);
      return {};
    }

    VoidResult Mediator::CancelController(ObjectId controller_id) noexcept {
      try {
        std::unique_lock<std::shared_mutex> lock(controller_mutex_);

        auto it = controller_map_.find(controller_id);
        if (it == controller_map_.end()) {
          return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
        }

        controller_map_.erase(it);

        std::cout << std::format("CancelController() 控制器ID {} 注销成功\n", controller_id);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::EnrollExecutor(ObjectId executor_id, std::shared_ptr<Executor> executor) {
      if (!executor) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      std::unique_lock<std::shared_mutex> lock(executor_mutex_);

      // 检查是否已经注册
      if (executor_map_.contains(executor_id)) {
        return std::unexpected(ErrorCode::ALREADY_EXISTS);
      }

      // 注册执行器
      executor_map_[executor_id] = executor;

      // 添加设备到执行器的映射关系
      auto result = AddDevToExecutorMap(executor);
      if (!result) {
        executor_map_.erase(executor_id);
        return result;
      }

      std::cout << std::format("EnrollExecutor() 执行器ID {} 注册成功\n", executor_id);
      return {};
    }

    VoidResult Mediator::CancelExecutor(ObjectId executor_id) noexcept {
      try {
        std::unique_lock<std::shared_mutex> lock(executor_mutex_);

        auto it = executor_map_.find(executor_id);
        if (it == executor_map_.end()) {
          return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
        }

        // 移除设备到执行器的映射关系
        RemoveDevToExecutorMap(it->second);

        executor_map_.erase(it);

        std::cout << std::format("CancelExecutor() 执行器ID {} 注销成功\n", executor_id);
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::SendCommonMsgToExecutor(ObjectId executor_id, const CommonMessage& msg,
                                                 ObjectId source_id) {
      auto executor = GetExecutorInfoByID(executor_id);
      if (!executor) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      // 调用执行器的推送命令接口
      auto result = executor->PushCommand(msg, source_id);
      if (!result) {
        return std::unexpected(result.error());
      }

      return {};
    }

    VoidResult Mediator::SendCommonMsgToController(ObjectId controller_id, const CommonMessage& msg,
                                                   ObjectId source_id) {
      auto controller = GetControllerInfoByID(controller_id);
      if (!controller) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      // 调用控制器的回复结果接口
      auto result = controller->ReplyResult(msg, source_id);
      if (!result) {
        return std::unexpected(result.error());
      }

      return {};
    }

    Result<size_t> Mediator::SendEventMsgToController(const EventMessage& msg, ObjectId source_id) {
      size_t success_count = 0;
      size_t total_count = 0;

      std::shared_lock<std::shared_mutex> lock(controller_mutex_);

      // 使用现代 C++ ranges 遍历所有控制器，找到关注此事件的控制器
      auto interested_controllers
          = controller_map_ | std::views::values | std::views::filter([&](const auto& controller) {
              return controller && controller->IsCare(msg.event_type, msg.device_uuid);
            });

      for (const auto& controller : interested_controllers) {
        total_count++;
        auto result = controller->PushEventNotify(msg, source_id);
        if (result) {
          success_count++;
        }
      }

      std::cout << std::format("事件通知发送完成，成功 {}/{} 个控制器\n", success_count,
                               total_count);
      return success_count;
    }

    Result<ObjectId> Mediator::GetExecutorIdByDevID(DeviceId device_id,
                                                    DeviceCategory device_type) const {
      DeviceUUID device_uuid(device_id, device_type);
      auto executor = GetExecutorInfoByDevID(device_uuid);

      if (!executor) {
        return std::unexpected(ErrorCode::OBJECT_NOT_FOUND);
      }

      ObjectId executor_id = executor->GetObjectId();
      return executor_id;
    }

    Mediator::Statistics Mediator::GetStatistics() const noexcept {
      try {
        Statistics stats;

        {
          std::shared_lock<std::shared_mutex> lock(controller_mutex_);
          stats.controller_count = controller_map_.size();
        }

        {
          std::shared_lock<std::shared_mutex> lock(executor_mutex_);
          stats.executor_count = executor_map_.size();
        }

        {
          std::shared_lock<std::shared_mutex> lock(dev_to_executor_mutex_);
          stats.device_mapping_count = dev_to_executor_map_.size();
        }

        return stats;
      } catch (...) {
        return Statistics{};  // 返回默认值
      }
    }

    VoidResult Mediator::ClearAll() noexcept {
      try {
        // 清理控制器映射
        {
          std::unique_lock<std::shared_mutex> lock(controller_mutex_);
          controller_map_.clear();
        }

        // 清理执行器映射
        {
          std::unique_lock<std::shared_mutex> lock(executor_mutex_);
          executor_map_.clear();
        }

        // 清理设备到执行器映射
        {
          std::unique_lock<std::shared_mutex> lock(dev_to_executor_mutex_);
          dev_to_executor_map_.clear();
        }

        std::cout << "Mediator::ClearAll() 所有注册对象已清理\n";
        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    std::shared_ptr<Controller> Mediator::GetControllerInfoByID(ObjectId controller_id) const {
      std::shared_lock<std::shared_mutex> lock(controller_mutex_);
      auto it = controller_map_.find(controller_id);
      return (it != controller_map_.end()) ? it->second : nullptr;
    }

    std::shared_ptr<Executor> Mediator::GetExecutorInfoByID(ObjectId executor_id) const {
      std::shared_lock<std::shared_mutex> lock(executor_mutex_);
      auto it = executor_map_.find(executor_id);
      return (it != executor_map_.end()) ? it->second : nullptr;
    }

    std::shared_ptr<Executor> Mediator::GetExecutorInfoByDevID(const DeviceUUID& device_id) const {
      std::shared_lock<std::shared_mutex> lock(dev_to_executor_mutex_);
      auto it = dev_to_executor_map_.find(device_id);
      return (it != dev_to_executor_map_.end()) ? it->second : nullptr;
    }

    VoidResult Mediator::AddDevToExecutorMap(std::shared_ptr<Executor> executor) {
      if (!executor) {
        return std::unexpected(ErrorCode::INVALID_PARAMETER);
      }

      const auto& reg_info = executor->GetRegObjProperty();
      if (!reg_info.has_devices()) {
        return {};  // 不关注设备，无需添加映射
      }

      try {
        std::unique_lock<std::shared_mutex> lock(dev_to_executor_mutex_);

        // 使用现代 C++ ranges 添加所有管理的设备到映射表
        for (const auto& device : reg_info.devices.value()) {
          dev_to_executor_map_[device] = executor;
        }

        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

    VoidResult Mediator::RemoveDevToExecutorMap(std::shared_ptr<Executor> executor) noexcept {
      if (!executor) {
        return {};
      }

      try {
        const auto& reg_info = executor->GetRegObjProperty();
        if (!reg_info.has_devices()) {
          return {};  // 不关注设备，无需移除映射
        }

        std::unique_lock<std::shared_mutex> lock(dev_to_executor_mutex_);

        // 使用现代 C++ ranges 移除所有管理的设备映射
        for (const auto& device : reg_info.devices.value()) {
          auto it = dev_to_executor_map_.find(device);
          if (it != dev_to_executor_map_.end() && it->second == executor) {
            dev_to_executor_map_.erase(it);
          }
        }

        return {};
      } catch (...) {
        return std::unexpected(ErrorCode::OPERATION_FAILED);
      }
    }

  }  // namespace base
}  // namespace zexuan
