/**
 * @file tcp_message_bus.cpp
 * @brief TCP消息总线服务器类实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/bus/tcp_bus_server.hpp"

#include <spdlog/spdlog.h>

#include <fstream>

#include "nlohmann/json.hpp"
#include "zexuan/event/net/address.hpp"

namespace zexuan {
  namespace bus {

    TcpBusServer::TcpBusServer(zexuan::event::EventLoop* event_loop, const std::string& config_path)
        : LifecycleComponentBase("TcpBusServer"),
          event_loop_(event_loop),
          config_path_(config_path) {
      if (!event_loop) {
        spdlog::error("{}: EventLoop cannot be null", GetComponentName());
        throw std::invalid_argument("EventLoop cannot be null");
      }

      spdlog::info("{}: Component created, ready to start", GetComponentName());

      spdlog::info("TcpBusServer created: {}:{}, threads: {}", host_, port_, thread_pool_size_);
    }

    TcpBusServer::~TcpBusServer() {
      // 确保在析构前调用Stop()
      if (GetState() != base::ComponentState::STOPPED) {
        spdlog::warn("{}: Component not stopped before destruction, forcing stop",
                     GetComponentName());
        Stop();
      }
      spdlog::info("{}: Component destroyed", GetComponentName());
    }

    bool TcpBusServer::loadConfig() {
      std::ifstream config_file(config_path_);
      if (!config_file.is_open()) {
        spdlog::error("{}: Cannot open config file: {}", GetComponentName(), config_path_);
        return false;
      }

      nlohmann::json config_json;
      try {
        config_file >> config_json;
      } catch (const std::exception& e) {
        spdlog::error("{}: Failed to parse config file {}: {}", GetComponentName(), config_path_,
                      e.what());
        return false;
      }

      // 读取总线服务器配置
      if (!config_json.contains("bus") || !config_json["bus"].contains("server")) {
        spdlog::error("{}: Missing bus.server section in config file: {}", GetComponentName(),
                      config_path_);
        return false;
      }

      auto bus_config = config_json["bus"]["server"];

      // 读取必需的配置项，不允许默认值
      if (!bus_config.contains("host")) {
        spdlog::error("{}: Missing required config: bus.server.host", GetComponentName());
        return false;
      }
      if (!bus_config.contains("port")) {
        spdlog::error("{}: Missing required config: bus.server.port", GetComponentName());
        return false;
      }
      if (!bus_config.contains("thread_pool_size")) {
        spdlog::error("{}: Missing required config: bus.server.thread_pool_size",
                      GetComponentName());
        return false;
      }
      if (!bus_config.contains("verbose_logging")) {
        spdlog::error("{}: Missing required config: bus.server.verbose_logging",
                      GetComponentName());
        return false;
      }
      if (!bus_config.contains("max_connections")) {
        spdlog::error("{}: Missing required config: bus.server.max_connections",
                      GetComponentName());
        return false;
      }

      host_ = bus_config["host"];
      port_ = bus_config["port"];
      thread_pool_size_ = bus_config["thread_pool_size"];
      verbose_logging_ = bus_config["verbose_logging"];
      max_connections_ = bus_config["max_connections"];

      spdlog::debug(
          "{}: Loaded configuration - host: {}:{}, threads: {}, max_conn: {}, verbose: {}",
          GetComponentName(), host_, port_, thread_pool_size_, max_connections_, verbose_logging_);
      return true;
    }

    bool TcpBusServer::Start() {
      if (!TransitionTo(base::ComponentState::STARTING)) {
        return false;
      }

      try {
        spdlog::info("{}: Starting component", GetComponentName());

        // 1. 加载配置
        spdlog::debug("{}: Loading configuration from {}", GetComponentName(), config_path_);
        if (!loadConfig()) {
          spdlog::error("{}: Failed to load configuration", GetComponentName());
          TransitionTo(base::ComponentState::ERROR);
          return false;
        }
        spdlog::info("{}: Configuration loaded successfully", GetComponentName());

        // 2. 创建订阅管理器
        spdlog::debug("{}: Creating subscription manager", GetComponentName());
        subscription_manager_ = std::make_shared<SubscriptionManager>();

        // 3. 创建消息路由器
        spdlog::debug("{}: Creating message router", GetComponentName());
        message_router_ = std::make_shared<MessageRouter>(subscription_manager_);
        message_router_->setVerboseLogging(verbose_logging_);

        // 4. 创建TCP服务器
        spdlog::debug("{}: Creating TCP server on {}:{}", GetComponentName(), host_, port_);
        zexuan::event::Address listen_addr(host_, port_);
        tcp_server_
            = std::make_unique<zexuan::event::TcpServer>(event_loop_, listen_addr, "TcpBusServer");

        // 5. 设置线程池大小
        spdlog::debug("{}: Setting thread pool size to {}", GetComponentName(), thread_pool_size_);
        tcp_server_->setThreadNum(thread_pool_size_);

        // 6. 设置回调函数
        spdlog::debug("{}: Setting up network callbacks", GetComponentName());
        tcp_server_->setConnectionCallback(
            [this](const TcpConnectionPtr& conn) { onConnection(conn); });

        tcp_server_->setMessageCallback(
            [this](const TcpConnectionPtr& conn, zexuan::event::Buffer* buf,
                   zexuan::event::Timestamp time) { onMessage(conn, buf, time); });

        // 7. 启动TCP服务器
        spdlog::debug("{}: Starting TCP server", GetComponentName());
        tcp_server_->start();
        running_.store(true);

        if (!TransitionTo(base::ComponentState::RUNNING)) {
          return false;
        }

        spdlog::info("{}: Component started successfully on {}:{}", GetComponentName(), host_,
                     port_);
        return true;

      } catch (const std::exception& e) {
        spdlog::error("{}: Failed to start component: {}", GetComponentName(), e.what());
        TransitionTo(base::ComponentState::ERROR);
        return false;
      }
    }

    bool TcpBusServer::Stop() {
      if (GetState() == base::ComponentState::STOPPED) {
        return true;  // 已经停止
      }

      if (!TransitionTo(base::ComponentState::STOPPING)) {
        return false;
      }

      spdlog::info("{}: Stopping component", GetComponentName());

      try {
        // 1. 停止接收新连接
        spdlog::debug("{}: Stopping TCP server", GetComponentName());
        running_.store(false);

        // 2. 清理所有客户端缓冲区
        spdlog::debug("{}: Cleaning up client buffers", GetComponentName());
        {
          std::lock_guard<std::mutex> lock(buffers_mutex_);
          client_buffers_.clear();
        }

        // 3. 清理订阅管理器和消息路由器
        spdlog::debug("{}: Cleaning up managers", GetComponentName());
        if (subscription_manager_) {
          subscription_manager_.reset();
        }
        if (message_router_) {
          message_router_.reset();
        }

        // 4. 清理TCP服务器
        spdlog::debug("{}: Cleaning up TCP server", GetComponentName());
        if (tcp_server_) {
          tcp_server_.reset();
        }

        if (!TransitionTo(base::ComponentState::STOPPED)) {
          return false;
        }

        spdlog::info("{}: Component stopped successfully", GetComponentName());
        return true;

      } catch (const std::exception& e) {
        spdlog::error("{}: Failed to stop component: {}", GetComponentName(), e.what());
        TransitionTo(base::ComponentState::ERROR);
        return false;
      }
    }

    size_t TcpBusServer::getConnectionCount() const {
      return subscription_manager_ ? subscription_manager_->getClientCount() : 0;
    }

    SubscriptionManager::Statistics TcpBusServer::getSubscriptionStatistics() const {
      return subscription_manager_ ? subscription_manager_->getStatistics()
                                   : SubscriptionManager::Statistics{};
    }

    RoutingStatistics TcpBusServer::getRoutingStatistics() const {
      return message_router_ ? message_router_->getStatistics() : RoutingStatistics{};
    }

    void TcpBusServer::resetStatistics() {
      if (message_router_) {
        message_router_->resetStatistics();
      }
    }

    size_t TcpBusServer::cleanupInvalidConnections() {
      size_t cleaned
          = subscription_manager_ ? subscription_manager_->cleanupInvalidConnections() : 0;

      // 清理无效的缓冲区
      {
        std::lock_guard<std::mutex> lock(buffers_mutex_);
        auto it = client_buffers_.begin();
        while (it != client_buffers_.end()) {
          if (!it->first || !it->first->connected()) {
            it = client_buffers_.erase(it);
          } else {
            ++it;
          }
        }
      }

      return cleaned;
    }

    size_t TcpBusServer::broadcastSystemMessage(const std::string& message) {
      if (!message_router_) {
        return 0;
      }

      zexuan::base::ControlMessage control_msg;
      control_msg.action = "system_message";
      control_msg.success = true;
      control_msg.message = message;

      return message_router_->broadcastControlMessage(control_msg);
    }

    void TcpBusServer::onConnection(const TcpConnectionPtr& conn) {
      if (!conn) {
        return;
      }

      if (conn->connected()) {
        // 检查连接数限制
        if (getConnectionCount() >= max_connections_) {
          spdlog::warn("Connection limit reached ({}), rejecting connection from {}",
                       max_connections_, conn->peerAddress().toIpPort());
          conn->shutdown();
          return;
        }

        // 添加客户端
        subscription_manager_->addClient(conn);

        // 创建消息缓冲区
        getOrCreateBuffer(conn);

        spdlog::info("Client connected: {} (total: {})", conn->peerAddress().toIpPort(),
                     getConnectionCount());

      } else {
        // 移除客户端
        subscription_manager_->removeClient(conn);

        // 移除消息缓冲区
        removeBuffer(conn);

        spdlog::info("Client disconnected: {} (total: {})", conn->peerAddress().toIpPort(),
                     getConnectionCount());
      }
    }

    void TcpBusServer::onMessage(const TcpConnectionPtr& conn, zexuan::event::Buffer* buf,
                                 zexuan::event::Timestamp receive_time) {
      if (!conn || !buf) {
        return;
      }

      size_t data_size = buf->readableBytes();
      if (verbose_logging_) {
        spdlog::debug("Received {} bytes from {}", data_size, conn->peerAddress().toIpPort());
      }

      // 获取消息缓冲区
      zexuan::utils::MessageBuffer* msg_buffer = getOrCreateBuffer(conn);
      if (!msg_buffer) {
        spdlog::error("Failed to get message buffer for connection: {}", conn->name());
        return;
      }

      // 将接收到的数据添加到缓冲区
      const char* data = buf->peek();

      msg_buffer->appendData(reinterpret_cast<const uint8_t*>(data), data_size);
      buf->retrieveAll();

      // 尝试提取完整消息
      std::optional<std::string> json_message;
      while ((json_message = msg_buffer->extractMessage()).has_value()) {
        if (verbose_logging_) {
          spdlog::trace("Processing complete message from {}", conn->peerAddress().toIpPort());
        }
        handleJsonMessage(conn, json_message.value());
      }
    }

    void TcpBusServer::handleJsonMessage(const TcpConnectionPtr& conn,
                                         const std::string& json_message) {
      try {
        nlohmann::json json = nlohmann::json::parse(json_message);

        std::string msg_category = getMessageCategory(json_message);

        if (msg_category == "subscription") {
          handleSubscriptionMessage(conn, json_message);
        } else if (msg_category == "common") {
          handleCommonMessage(conn, json_message);
        } else if (msg_category == "event") {
          handleEventMessage(conn, json_message);
        } else {
          spdlog::warn("Unknown message category: {} from {}", msg_category,
                       conn->peerAddress().toIpPort());
          sendErrorResponse(conn, "Unknown message category: " + msg_category);
        }

      } catch (const std::exception& e) {
        spdlog::error("Failed to handle JSON message from {}: {}", conn->peerAddress().toIpPort(),
                      e.what());
        sendErrorResponse(conn, "Invalid JSON message: " + std::string(e.what()));
      }
    }

    void TcpBusServer::handleSubscriptionMessage(const TcpConnectionPtr& conn,
                                                 const std::string& json_str) {
      auto subscription_msg
          = zexuan::utils::MessageSerializer::deserializeSubscriptionMessage(json_str);
      if (!subscription_msg) {
        spdlog::error("Failed to deserialize subscription message from {}",
                      conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid subscription message format");
        return;
      }

      // 处理订阅请求
      zexuan::base::ControlMessage response
          = subscription_manager_->handleSubscription(conn, subscription_msg.value());

      // 发送响应
      if (!message_router_->sendControlMessage(conn, response)) {
        spdlog::error("Failed to send subscription response to {}", conn->peerAddress().toIpPort());
      }
    }

    void TcpBusServer::handleCommonMessage(const TcpConnectionPtr& conn,
                                           const std::string& json_str) {
      auto common_msg = zexuan::utils::MessageSerializer::deserializeCommonMessage(json_str);
      if (!common_msg) {
        spdlog::error("Failed to deserialize common message from {}",
                      conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid common message format");
        return;
      }

      // 路由消息
      size_t routed_count = message_router_->routeCommonMessage(conn, common_msg.value());

      if (verbose_logging_) {
        spdlog::debug("Routed CommonMessage from {} to {} subscribers",
                      conn->peerAddress().toIpPort(), routed_count);
      }
    }

    void TcpBusServer::handleEventMessage(const TcpConnectionPtr& conn,
                                          const std::string& json_str) {
      auto event_msg = zexuan::utils::MessageSerializer::deserializeEventMessage(json_str);
      if (!event_msg) {
        spdlog::error("Failed to deserialize event message from {}",
                      conn->peerAddress().toIpPort());
        sendErrorResponse(conn, "Invalid event message format");
        return;
      }

      // 路由消息
      size_t routed_count = message_router_->routeEventMessage(conn, event_msg.value());

      if (verbose_logging_) {
        spdlog::debug("Routed EventMessage from {} to {} subscribers",
                      conn->peerAddress().toIpPort(), routed_count);
      }
    }

    zexuan::utils::MessageBuffer* TcpBusServer::getOrCreateBuffer(const TcpConnectionPtr& conn) {
      if (!conn) {
        return nullptr;
      }

      std::lock_guard<std::mutex> lock(buffers_mutex_);

      auto it = client_buffers_.find(conn);
      if (it != client_buffers_.end()) {
        return it->second.get();
      }

      // 创建新的缓冲区
      auto buffer = std::make_unique<zexuan::utils::MessageBuffer>();
      zexuan::utils::MessageBuffer* buffer_ptr = buffer.get();
      client_buffers_[conn] = std::move(buffer);

      if (verbose_logging_) {
        spdlog::debug("Created message buffer for connection: {}", conn->peerAddress().toIpPort());
      }

      return buffer_ptr;
    }

    void TcpBusServer::removeBuffer(const TcpConnectionPtr& conn) {
      if (!conn) {
        return;
      }

      std::lock_guard<std::mutex> lock(buffers_mutex_);
      auto it = client_buffers_.find(conn);
      if (it != client_buffers_.end()) {
        if (verbose_logging_) {
          spdlog::debug("Removed message buffer for connection: {}",
                        conn->peerAddress().toIpPort());
        }
        client_buffers_.erase(it);
      }
    }

    void TcpBusServer::sendErrorResponse(const TcpConnectionPtr& conn,
                                         const std::string& error_message) {
      if (!conn || !message_router_) {
        return;
      }

      zexuan::base::ControlMessage error_response;
      error_response.action = "error";
      error_response.success = false;
      error_response.message = error_message;

      message_router_->sendControlMessage(conn, error_response);
    }

    std::string TcpBusServer::getMessageCategory(const std::string& json_str) {
      try {
        nlohmann::json json = nlohmann::json::parse(json_str);

        // 检查是否为订阅消息
        if (json.contains("action")
            && (json["action"] == "subscribe" || json["action"] == "unsubscribe")) {
          return "subscription";
        }

        // 检查是否有msg_category字段
        if (json.contains("msg_category")) {
          return json["msg_category"].get<std::string>();
        }

        // 根据字段推断消息类型
        if (json.contains("type") && json.contains("source_id") && json.contains("target_id")) {
          return "common";
        }

        if (json.contains("event_type") && json.contains("device_uuid")) {
          return "event";
        }

        return "unknown";

      } catch (const std::exception&) {
        return "unknown";
      }
    }

  }  // namespace bus
}  // namespace zexuan
