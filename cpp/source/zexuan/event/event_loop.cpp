#include "zexuan/event/event_loop.hpp"

#include <signal.h>
#include <spdlog/spdlog.h>
#include <sys/eventfd.h>
#include <unistd.h>

#include <cassert>
#include <cstring>

#include "zexuan/event/channel.hpp"
#include "zexuan/event/sys/eventfd_handler.hpp"
#include "zexuan/event/sys/inotify_handler.hpp"
#include "zexuan/event/sys/signal_handler.hpp"
#include "zexuan/event/sys/timer_handler.hpp"

using namespace zexuan;
using namespace zexuan::event;

namespace {
  __thread EventLoop* t_loopInThisThread = 0;

  const int kPollTimeMs = 10000;

#pragma GCC diagnostic ignored "-Wold-style-cast"
  class IgnoreSigPipe {
  public:
    IgnoreSigPipe() { ::signal(SIGPIPE, SIG_IGN); }
  };
#pragma GCC diagnostic error "-Wold-style-cast"

  IgnoreSigPipe initObj;
}  // namespace

EventLoop* EventLoop::getEventLoopOfCurrentThread() { return t_loopInThisThread; }

EventLoop::EventLoop()
    : looping_(false),
      quit_(false),
      eventHandling_(false),
      callingPendingFunctors_(false),
      iteration_(0),
      threadId_(std::this_thread::get_id()),
      poller_(Poller::createDefaultPoller()),
      currentActiveChannel_(NULL),
      timerHandler_(std::make_unique<TimerHandler>(this)),
      signalHandler_(std::make_unique<SignalHandler>(this)),
      eventfdHandler_(std::make_unique<EventfdHandler>(this)),
      inotifyHandler_(std::make_unique<InotifyHandler>(this)) {
  spdlog::debug("EventLoop created {}", static_cast<void*>(this));
  if (t_loopInThisThread) {
    spdlog::critical("Another EventLoop {} exists in this thread",
                     static_cast<void*>(t_loopInThisThread));
  } else {
    t_loopInThisThread = this;
  }

  // 设置 EventfdHandler 的回调来处理 wakeup 事件
  // 只需要触发一下即可，doPendingFunctors() 会在每轮循环末尾自动执行
  eventfdHandler_->setNotifyCallback([]() {
    // 空回调即可，EventLoop主循环会处理pending functors
  });
}

EventLoop::~EventLoop() {
  spdlog::debug("EventLoop {} destructs", static_cast<void*>(this));

  // 在设置 t_loopInThisThread = NULL 之前，先显式析构包含 Channel 的成员
  // 这样它们的 Channel::remove() 调用时 isInLoopThread() 仍然返回 true
  inotifyHandler_.reset();
  eventfdHandler_.reset();
  signalHandler_.reset();
  timerHandler_.reset();

  t_loopInThisThread = NULL;
}

void EventLoop::loop() {
  assert(!looping_);
  assertInLoopThread();
  looping_ = true;
  quit_ = false;  // FIXME: what if someone calls quit() before loop() ?
  spdlog::trace("EventLoop {} start looping", static_cast<void*>(this));

  while (!quit_) {
    activeChannels_.clear();
    pollReturnTime_ = poller_->poll(kPollTimeMs, &activeChannels_);
    ++iteration_;

    // 只在有活跃通道时记录trace日志，避免空轮询的高频日志
    if (!activeChannels_.empty()) {
      spdlog::trace("EventLoop {} poll {} channels", static_cast<void*>(this),
                    activeChannels_.size());
    }

    // TODO sort channel by priority
    eventHandling_ = true;
    for (Channel* channel : activeChannels_) {
      currentActiveChannel_ = channel;
      currentActiveChannel_->handleEvent(pollReturnTime_);
    }
    currentActiveChannel_ = NULL;
    eventHandling_ = false;
    doPendingFunctors();
  }

  spdlog::trace("EventLoop {} stop looping", static_cast<void*>(this));
  looping_ = false;
}

void EventLoop::quit() {
  quit_ = true;
  // There is a chance that loop() just executed while(!quit_) and exited,
  // then EventLoop destructs, then we are accessing an invalid object.
  // Can be fixed using mutex_ in both places.
  if (!isInLoopThread()) {
    wakeup();
  }
}

void EventLoop::runInLoop(Functor cb) {
  if (isInLoopThread()) {
    cb();
  } else {
    queueInLoop(std::move(cb));
  }
}

void EventLoop::queueInLoop(Functor cb) {
  {
    std::lock_guard<std::mutex> lock(mutex_);
    pendingFunctors_.push_back(std::move(cb));
  }

  if (!isInLoopThread() || callingPendingFunctors_) {
    wakeup();
  }
}

size_t EventLoop::queueSize() const {
  std::lock_guard<std::mutex> lock(mutex_);
  return pendingFunctors_.size();
}

void EventLoop::wakeup() { eventfdHandler_->notify(1); }

void EventLoop::doPendingFunctors() {
  std::vector<Functor> functors;
  callingPendingFunctors_ = true;

  {
    std::lock_guard<std::mutex> lock(mutex_);
    functors.swap(pendingFunctors_);
  }

  for (const Functor& functor : functors) {
    functor();
  }
  callingPendingFunctors_ = false;
}

void EventLoop::updateChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  poller_->updateChannel(channel);
}

void EventLoop::removeChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  if (eventHandling_) {
    assert(currentActiveChannel_ == channel
           || std::find(activeChannels_.begin(), activeChannels_.end(), channel)
                  == activeChannels_.end());
  }
  poller_->removeChannel(channel);
}

bool EventLoop::hasChannel(Channel* channel) {
  assert(channel->ownerLoop() == this);
  assertInLoopThread();
  return poller_->hasChannel(channel);
}

void EventLoop::abortNotInLoopThread() { spdlog::critical("EventLoop: abortNotInLoopThread"); }

void EventLoop::printActiveChannels() const {
  for (const Channel* channel : activeChannels_) {
    spdlog::trace("  {}", channel->reventsToString());
  }
}

// 定时器接口实现
TimerId EventLoop::runAt(Timestamp when, Functor cb) {
  return timerHandler_->runAt(when, std::move(cb));
}

TimerId EventLoop::runAfter(double delay, Functor cb) {
  return timerHandler_->runAfter(delay, std::move(cb));
}

TimerId EventLoop::runEvery(double interval, Functor cb) {
  return timerHandler_->runEvery(interval, std::move(cb));
}

void EventLoop::cancel(TimerId timerId) { timerHandler_->cancel(timerId); }

// 信号处理接口实现
void EventLoop::registerSignal(int signal, std::function<void(int)> cb) {
  signalHandler_->registerSignal(signal, std::move(cb));
}

void EventLoop::unregisterSignal(int signal) { signalHandler_->unregisterSignal(signal); }

void EventLoop::registerShutdownSignals(std::function<void(int)> cb) {
  signalHandler_->registerShutdownSignals(std::move(cb));
}

void EventLoop::registerUserSignals(std::function<void(int)> cb) {
  signalHandler_->registerUserSignals(std::move(cb));
}

// EventfdHandler 接口实现
void EventLoop::setEventfdNotifyCallback(std::function<void()> cb) {
  eventfdHandler_->setNotifyCallback(std::move(cb));
}

void EventLoop::notifyEventfd(uint64_t count) { eventfdHandler_->notify(count); }

// InotifyHandler 接口实现
WatchId EventLoop::watchFile(const std::string& path, FileEventCallback cb) {
  return inotifyHandler_->watchFile(path, std::move(cb));
}

WatchId EventLoop::watchDirectory(const std::string& path, FileEventCallback cb) {
  return inotifyHandler_->watchDirectory(path, std::move(cb));
}

void EventLoop::unwatchFile(WatchId watchId) { inotifyHandler_->removeWatch(watchId); }
