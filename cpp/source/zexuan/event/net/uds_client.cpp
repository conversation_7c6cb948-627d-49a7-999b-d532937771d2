#include "zexuan/event/net/uds_client.hpp"

#include <spdlog/spdlog.h>

#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/sockets_ops.hpp"
#include "zexuan/event/net/uds_connector.hpp"

using namespace zexuan;
using namespace zexuan::event;

UdsClient::UdsClient(EventLoop* loop, const std::string& serverPath, const std::string& nameArg)
    : loop_(loop),
      connector_(new UdsConnector(loop, serverPath)),
      serverPath_(serverPath),
      name_(nameArg),
      retry_(false),
      connect_(true),
      nextConnId_(1) {
  connector_->setNewConnectionCallback([this](int sockfd) { newConnection(sockfd); });
  // FIXME setConnectFailedCallback
  spdlog::debug("UdsClient::UdsClient[{}] - connector {}", name_,
                static_cast<void*>(connector_.get()));
}

UdsClient::~UdsClient() {
  spdlog::debug("UdsClient::~UdsClient[{}] - connector {}", name_,
                static_cast<void*>(connector_.get()));

  UdsConnectionPtr conn;
  bool unique = false;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    unique = connection_.unique();
    conn = connection_;
  }

  if (conn) {
    assert(loop_ == conn->getLoop());
    // FIXME: not 100% safe, if we are in different thread
    UdsCloseCallback cb = [this](const UdsConnectionPtr& conn) {
      // FIXME: unsafe
      loop_->runInLoop([this, conn]() { removeConnection(conn); });
    };
    loop_->runInLoop([conn, cb]() { conn->setCloseCallback(cb); });
    if (unique) {
      conn->forceClose();
    }
  } else {
    connector_->stop();
    // FIXME: implement proper cleanup without timer
  }
}

void UdsClient::connect() {
  // FIXME: check state
  spdlog::info("UdsClient::connect[{}] - connecting to {}", name_, serverPath_);
  connect_ = true;
  connector_->start();
}

void UdsClient::disconnect() {
  connect_ = false;

  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_) {
      connection_->shutdown();
    }
  }
}

void UdsClient::stop() {
  connect_ = false;
  connector_->stop();
}

void UdsClient::newConnection(int sockfd) {
  loop_->assertInLoopThread();

  char buf[32];
  snprintf(buf, sizeof buf, ":%s#%d", serverPath_.c_str(), nextConnId_);
  ++nextConnId_;
  std::string connName = name_ + buf;

  // 对于客户端，localPath通常为空，peerPath是服务器路径
  UdsConnectionPtr conn(new UdsConnection(loop_, connName, sockfd, "", serverPath_));

  conn->setConnectionCallback(connectionCallback_);
  conn->setMessageCallback(messageCallback_);
  conn->setWriteCompleteCallback(writeCompleteCallback_);
  conn->setCloseCallback([this](const UdsConnectionPtr& conn) { removeConnection(conn); });

  {
    std::lock_guard<std::mutex> lock(mutex_);
    connection_ = conn;
  }

  conn->connectEstablished();
}

void UdsClient::removeConnection(const UdsConnectionPtr& conn) {
  loop_->assertInLoopThread();
  assert(loop_ == conn->getLoop());

  {
    std::lock_guard<std::mutex> lock(mutex_);
    assert(connection_ == conn);
    connection_.reset();
  }

  loop_->queueInLoop([conn]() { conn->connectDestroyed(); });

  if (retry_ && connect_) {
    spdlog::info("UdsClient::connect[{}] - Reconnecting to {}", name_, serverPath_);
    connector_->restart();
  }
}
