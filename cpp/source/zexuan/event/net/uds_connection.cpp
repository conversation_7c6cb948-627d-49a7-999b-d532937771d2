#include "zexuan/event/net/uds_connection.hpp"

#include <errno.h>
#include <spdlog/spdlog.h>

#include <cassert>

#include "zexuan/event/channel.hpp"
#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/socket.hpp"
#include "zexuan/event/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::event;

void defaultConnectionCallback(const UdsConnectionPtr& conn) {
  if (conn->connected()) {
    spdlog::info("UDS connection established: {} -> {}", conn->localPath(), conn->peerPath());
    spdlog::debug("UDS connection {} created with fd {}", conn->name(), conn->fd());
  } else {
    spdlog::info("UDS connection closed: {}", conn->peerPath());
    spdlog::debug("UDS connection {} destroyed", conn->name());
  }
  // do not call conn->forceClose(), because some users want to register message callback only.
}

void defaultMessageCallback(const UdsConnectionPtr& conn, Buffer* buf, Timestamp receiveTime) {
  size_t readable = buf->readableBytes();
  spdlog::debug("UDS received {} bytes from {}", readable, conn->peerPath());
  spdlog::trace("UDS message received at {}", std::chrono::duration_cast<std::chrono::microseconds>(
                                                  receiveTime.time_since_epoch())
                                                  .count());
  buf->retrieveAll();
}

UdsConnection::UdsConnection(EventLoop* loop, const std::string& nameArg, int sockfd,
                             const std::string& localPath, const std::string& peerPath)
    : loop_(loop),
      name_(nameArg),
      state_(kConnecting),
      reading_(true),
      socket_(new Socket(sockfd)),
      channel_(new Channel(loop, sockfd)),
      localPath_(localPath),
      peerPath_(peerPath),
      highWaterMark_(64 * 1024 * 1024) {
  channel_->setReadCallback([this](Timestamp ts) { handleRead(ts); });
  channel_->setWriteCallback([this]() { handleWrite(); });
  channel_->setCloseCallback([this]() { handleClose(); });
  channel_->setErrorCallback([this]() { handleError(); });
  spdlog::debug("UdsConnection::ctor[{}] at {} fd={}", name_, static_cast<void*>(this), sockfd);
  // UDS connections don't support TCP keep-alive
}

UdsConnection::~UdsConnection() {
  spdlog::debug("UdsConnection::dtor[{}] at {} fd={} state={}", name_, static_cast<void*>(this),
                channel_->fd(), stateToString());
  assert(state_ == kDisconnected);
}

// UDS connections don't have TCP info

void UdsConnection::send(const void* data, int len) {
  send(std::string_view(static_cast<const char*>(data), len));
}

void UdsConnection::send(const std::string_view& message) {
  if (state_ == kConnected) {
    if (loop_->isInLoopThread()) {
      sendInLoop(message);
    } else {
      void (UdsConnection::*fp)(const std::string_view& message) = &UdsConnection::sendInLoop;
      loop_->runInLoop([this, fp, message]() { (this->*fp)(message); });
    }
  }
}

void UdsConnection::send(Buffer* buf) {
  if (state_ == kConnected) {
    if (loop_->isInLoopThread()) {
      sendInLoop(buf->peek(), buf->readableBytes());
      buf->retrieveAll();
    } else {
      void (UdsConnection::*fp)(const std::string_view& message) = &UdsConnection::sendInLoop;
      loop_->runInLoop([this, fp, buf]() {
        (this->*fp)(std::string_view(buf->peek(), buf->readableBytes()));
        buf->retrieveAll();
      });
    }
  }
}

void UdsConnection::shutdown() {
  // FIXME: use compare and swap
  if (state_ == kConnected) {
    setState(kDisconnecting);
    // FIXME: shared_from_this()?
    loop_->runInLoop([this]() { shutdownInLoop(); });
  }
}

void UdsConnection::forceClose() {
  // FIXME: use compare and swap
  if (state_ == kConnected || state_ == kDisconnecting) {
    setState(kDisconnecting);
    loop_->queueInLoop([this]() { forceCloseInLoop(); });
  }
}

void UdsConnection::forceCloseWithDelay(double seconds) {
  if (state_ == kConnected || state_ == kDisconnecting) {
    setState(kDisconnecting);
    // FIXME: implement timer for delayed close
    // For now, close immediately
    loop_->queueInLoop([this]() { forceCloseInLoop(); });
  }
}

// UDS connections don't support TCP_NODELAY

void UdsConnection::startRead() {
  loop_->runInLoop([this]() { startReadInLoop(); });
}

void UdsConnection::stopRead() {
  loop_->runInLoop([this]() { stopReadInLoop(); });
}

void UdsConnection::startReadInLoop() {
  loop_->assertInLoopThread();
  if (!reading_ || !channel_->isReading()) {
    channel_->enableReading();
    reading_ = true;
  }
}

void UdsConnection::stopReadInLoop() {
  loop_->assertInLoopThread();
  if (reading_ || channel_->isReading()) {
    channel_->disableReading();
    reading_ = false;
  }
}

void UdsConnection::connectEstablished() {
  loop_->assertInLoopThread();
  assert(state_ == kConnecting);
  setState(kConnected);
  channel_->tie(shared_from_this());
  channel_->enableReading();

  connectionCallback_(shared_from_this());
}

void UdsConnection::connectDestroyed() {
  loop_->assertInLoopThread();
  if (state_ == kConnected) {
    setState(kDisconnected);
    channel_->disableAll();

    connectionCallback_(shared_from_this());
  }
  channel_->remove();
}

void UdsConnection::handleRead(Timestamp receiveTime) {
  loop_->assertInLoopThread();
  int savedErrno = 0;
  ssize_t n = inputBuffer_.readFd(channel_->fd(), &savedErrno);
  if (n > 0) {
    messageCallback_(shared_from_this(), &inputBuffer_, receiveTime);
  } else if (n == 0) {
    handleClose();
  } else {
    errno = savedErrno;
    spdlog::error("UdsConnection::handleRead");
    handleError();
  }
}

void UdsConnection::handleWrite() {
  loop_->assertInLoopThread();
  if (channel_->isWriting()) {
    ssize_t n = sockets::write(channel_->fd(), outputBuffer_.peek(), outputBuffer_.readableBytes());
    if (n > 0) {
      outputBuffer_.retrieve(n);
      if (outputBuffer_.readableBytes() == 0) {
        channel_->disableWriting();
        if (writeCompleteCallback_) {
          loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
        }
        if (state_ == kDisconnecting) {
          shutdownInLoop();
        }
      }
    } else {
      spdlog::error("UdsConnection::handleWrite");
      // if (n < 0 && errno != EWOULDBLOCK)
      // {
      //   LOG_SYSERR << "UdsConnection::handleWrite";
      // }
    }
  } else {
    spdlog::trace("Connection fd = {} is down, no more writing", channel_->fd());
  }
}

void UdsConnection::handleClose() {
  loop_->assertInLoopThread();
  spdlog::trace("fd = {} state = {}", channel_->fd(), stateToString());
  assert(state_ == kConnected || state_ == kDisconnecting);
  // we don't close fd, leave it to dtor, so we can find leaks easily.
  setState(kDisconnected);
  channel_->disableAll();

  UdsConnectionPtr guardThis(shared_from_this());
  connectionCallback_(guardThis);
  // must be the last line
  closeCallback_(guardThis);
}

void UdsConnection::handleError() {
  int err = sockets::getSocketError(channel_->fd());
  spdlog::error("UdsConnection::handleError [{}] - SO_ERROR = {} {}", name_, err, strerror(err));
}

void UdsConnection::sendInLoop(const std::string_view& message) {
  sendInLoop(message.data(), message.size());
}

void UdsConnection::sendInLoop(const void* data, size_t len) {
  loop_->assertInLoopThread();
  ssize_t nwrote = 0;
  size_t remaining = len;
  bool faultError = false;
  if (state_ == kDisconnected) {
    spdlog::warn("disconnected, give up writing");
    return;
  }
  // if no thing in output queue, try writing directly
  if (!channel_->isWriting() && outputBuffer_.readableBytes() == 0) {
    nwrote = sockets::write(channel_->fd(), data, len);
    if (nwrote >= 0) {
      remaining = len - nwrote;
      if (remaining == 0 && writeCompleteCallback_) {
        loop_->queueInLoop([this]() { writeCompleteCallback_(shared_from_this()); });
      }
    } else  // nwrote < 0
    {
      nwrote = 0;
      if (errno != EWOULDBLOCK) {
        spdlog::error("UdsConnection::sendInLoop");
        if (errno == EPIPE || errno == ECONNRESET)  // FIXME: any others?
        {
          faultError = true;
        }
      }
    }
  }

  assert(remaining <= len);
  if (!faultError && remaining > 0) {
    size_t oldLen = outputBuffer_.readableBytes();
    if (oldLen + remaining >= highWaterMark_ && oldLen < highWaterMark_ && highWaterMarkCallback_) {
      loop_->queueInLoop([this, oldLen, remaining]() {
        highWaterMarkCallback_(shared_from_this(), oldLen + remaining);
      });
    }
    outputBuffer_.append(static_cast<const char*>(data) + nwrote, remaining);
    if (!channel_->isWriting()) {
      channel_->enableWriting();
    }
  }
}

void UdsConnection::shutdownInLoop() {
  loop_->assertInLoopThread();
  if (!channel_->isWriting()) {
    // we are not writing
    socket_->shutdownWrite();
  }
}

void UdsConnection::forceCloseInLoop() {
  loop_->assertInLoopThread();
  if (state_ == kConnected || state_ == kDisconnecting) {
    // as if we received 0 byte in handleRead();
    handleClose();
  }
}

const char* UdsConnection::stateToString() const {
  switch (state_) {
    case kDisconnected:
      return "kDisconnected";
    case kConnecting:
      return "kConnecting";
    case kConnected:
      return "kConnected";
    case kDisconnecting:
      return "kDisconnecting";
    default:
      return "unknown state";
  }
}
