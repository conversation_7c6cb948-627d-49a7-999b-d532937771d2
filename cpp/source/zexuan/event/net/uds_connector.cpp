#include "zexuan/event/net/uds_connector.hpp"

#include <errno.h>
#include <spdlog/spdlog.h>

#include <algorithm>
#include <cassert>
#include <cstring>

#include "zexuan/event/event_loop.hpp"
#include "zexuan/event/net/sockets_ops.hpp"

using namespace zexuan;
using namespace zexuan::event;

const int UdsConnector::kMaxRetryDelayMs;

UdsConnector::UdsConnector(EventLoop* loop, const std::string& serverPath)
    : loop_(loop),
      serverPath_(serverPath),
      connect_(false),
      state_(kDisconnected),
      retryDelayMs_(kInitRetryDelayMs) {
  spdlog::debug("UdsConnector ctor[{}]", static_cast<void*>(this));
}

UdsConnector::~UdsConnector() {
  spdlog::debug("UdsConnector dtor[{}]", static_cast<void*>(this));
  assert(!channel_);
}

void UdsConnector::start() {
  connect_ = true;
  loop_->runInLoop([this]() { startInLoop(); });
}

void UdsConnector::startInLoop() {
  loop_->assertInLoopThread();
  assert(state_ == kDisconnected);
  if (connect_) {
    connect();
  } else {
    spdlog::debug("do not connect");
  }
}

void UdsConnector::stop() {
  connect_ = false;
  loop_->queueInLoop([this]() { stopInLoop(); });
}

void UdsConnector::stopInLoop() {
  loop_->assertInLoopThread();
  if (state_ == kConnecting) {
    setState(kDisconnected);
    int sockfd = removeAndResetChannel();
    retry(sockfd);
  }
}

void UdsConnector::connect() {
  int sockfd = sockets::createUdsNonblockingOrDie();
  spdlog::trace("Created UDS socket fd {} for connection to {}", sockfd, serverPath_);

  int ret = sockets::connectUds(sockfd, serverPath_.c_str());
  int savedErrno = (ret == 0) ? 0 : errno;
  switch (savedErrno) {
    case 0:
    case EINPROGRESS:
    case EINTR:
    case EISCONN:
      connecting(sockfd);
      break;

    case EAGAIN:
    case EADDRINUSE:
    case EADDRNOTAVAIL:
    case ECONNREFUSED:
    case ENETUNREACH:
      retry(sockfd);
      break;

    case EACCES:
    case EPERM:
    case EAFNOSUPPORT:
    case EALREADY:
    case EBADF:
    case EFAULT:
    case ENOTSOCK:
      spdlog::error("connect error in UdsConnector::connect {}", strerror(savedErrno));
      spdlog::trace("Closing UDS socket fd {} due to error", sockfd);
      sockets::close(sockfd);
      break;

    default:
      spdlog::error("Unexpected error in UdsConnector::connect {}", strerror(savedErrno));
      spdlog::trace("Closing UDS socket fd {} due to unexpected error", sockfd);
      sockets::close(sockfd);
      // connectErrorCallback_();
      break;
  }
}

void UdsConnector::restart() {
  loop_->assertInLoopThread();
  setState(kDisconnected);
  retryDelayMs_ = kInitRetryDelayMs;
  connect_ = true;
  startInLoop();
}

void UdsConnector::connecting(int sockfd) {
  setState(kConnecting);
  assert(!channel_);
  channel_.reset(new Channel(loop_, sockfd));
  channel_->setWriteCallback([this]() { handleWrite(); });
  channel_->setErrorCallback([this]() { handleError(); });

  // channel_->tie(shared_from_this()); is not working,
  // as channel_ is not managed by shared_ptr
  channel_->enableWriting();
}

void UdsConnector::handleWrite() {
  spdlog::trace("UdsConnector::handleWrite {}", static_cast<int>(state_));

  if (state_ == kConnecting) {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    if (err) {
      spdlog::warn("UdsConnector::handleWrite - SO_ERROR = {} {}", err, strerror(err));
      retry(sockfd);
    } else if (sockets::isSelfConnect(sockfd)) {
      spdlog::warn("UdsConnector::handleWrite - Self connect");
      retry(sockfd);
    } else {
      setState(kConnected);
      if (connect_) {
        newConnectionCallback_(sockfd);
      } else {
        sockets::close(sockfd);
      }
    }
  } else {
    // what happened?
    assert(state_ == kDisconnected);
  }
}

void UdsConnector::handleError() {
  spdlog::error("UdsConnector::handleError state={}", static_cast<int>(state_));
  if (state_ == kConnecting) {
    int sockfd = removeAndResetChannel();
    int err = sockets::getSocketError(sockfd);
    spdlog::trace("SO_ERROR = {} {}", err, strerror(err));
    retry(sockfd);
  }
}

void UdsConnector::retry(int sockfd) {
  sockets::close(sockfd);
  setState(kDisconnected);
  if (connect_) {
    spdlog::info("UdsConnector::retry - Retrying connecting to {} in {} milliseconds. ",
                 serverPath_, retryDelayMs_);
    // FIXME: implement timer for delayed retry
    // For now, we don't retry automatically
    retryDelayMs_ = std::min(retryDelayMs_ * 2, kMaxRetryDelayMs);
  } else {
    spdlog::debug("do not connect");
  }
}

int UdsConnector::removeAndResetChannel() {
  channel_->disableAll();
  channel_->remove();
  int sockfd = channel_->fd();
  // Can't reset channel_ here, because we are inside Channel::handleEvent
  loop_->queueInLoop([this]() { resetChannel(); });
  return sockfd;
}

void UdsConnector::resetChannel() { channel_.reset(); }
