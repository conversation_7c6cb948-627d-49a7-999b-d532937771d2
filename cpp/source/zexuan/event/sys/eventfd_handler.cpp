#include "zexuan/event/sys/eventfd_handler.hpp"

#include <spdlog/spdlog.h>
#include <sys/eventfd.h>
#include <unistd.h>

#include <cerrno>
#include <cstring>

#include "zexuan/event/channel.hpp"
#include "zexuan/event/event_loop.hpp"

namespace zexuan {
  namespace event {

    namespace {
      int createEventfd() {
        int evtfd = ::eventfd(0, EFD_NONBLOCK | EFD_CLOEXEC);
        if (evtfd < 0) {
          spdlog::error("Failed to create eventfd: {}", strerror(errno));
          abort();
        }
        spdlog::debug("Created eventfd: {}", evtfd);
        return evtfd;
      }

      uint64_t readEventfd(int fd) {
        uint64_t value;
        ssize_t n = ::read(fd, &value, sizeof(value));
        if (n != sizeof(value)) {
          if (n < 0) {
            if (errno != EAGAIN && errno != EWOULDBLOCK) {
              spdlog::error("Failed to read eventfd: {}", strerror(errno));
            }
          } else {
            spdlog::error("Partial read from eventfd: {} bytes", n);
          }
          return 0;
        }
        return value;
      }

      void writeEventfd(int fd, uint64_t value) {
        ssize_t n = ::write(fd, &value, sizeof(value));
        if (n != sizeof(value)) {
          spdlog::error("Failed to write eventfd: {}", strerror(errno));
        }
      }
    }  // namespace

    EventfdHandler::EventfdHandler(EventLoop* loop)
        : loop_(loop),
          eventfd_(createEventfd()),
          eventfdChannel_(std::make_unique<Channel>(loop, eventfd_)),
          callbackSet_(false) {
      loop_->assertInLoopThread();

      eventfdChannel_->setReadCallback([this](Timestamp) { handleRead(); });

      spdlog::debug("EventfdHandler created with eventfd: {}", eventfd_);
    }

    EventfdHandler::~EventfdHandler() {
      spdlog::debug("EventfdHandler destructor");

      if (eventfdChannel_) {
        eventfdChannel_->disableAll();
        eventfdChannel_->remove();
      }

      if (eventfd_ >= 0) {
        ::close(eventfd_);
      }
    }

    void EventfdHandler::setNotifyCallback(NotifyCallback cb) {
      loop_->runInLoop(
          [this, cb = std::move(cb)]() mutable { setNotifyCallbackInLoop(std::move(cb)); });
    }

    void EventfdHandler::setNotifyCallbackInLoop(NotifyCallback cb) {
      loop_->assertInLoopThread();

      notifyCallback_ = std::move(cb);
      callbackSet_.store(true);

      // 如果设置了回调函数，启用读取事件
      if (notifyCallback_) {
        if (!eventfdChannel_->isReading()) {
          eventfdChannel_->enableReading();
        }
      } else {
        if (eventfdChannel_->isReading()) {
          eventfdChannel_->disableReading();
        }
      }

      spdlog::debug("EventfdHandler callback set");
    }

    void EventfdHandler::notify(uint64_t count) {
      if (count == 0) {
        return;
      }

      writeEventfd(eventfd_, count);
      spdlog::trace("EventfdHandler notified with count: {}", count);
    }

    uint64_t EventfdHandler::getCount() {
      loop_->assertInLoopThread();
      return readEventfd(eventfd_);
    }

    void EventfdHandler::reset() {
      loop_->assertInLoopThread();

      // 读取所有待处理的事件，清空计数器
      uint64_t count = readEventfd(eventfd_);
      if (count > 0) {
        spdlog::debug("EventfdHandler reset, cleared count: {}", count);
      }
    }

    void EventfdHandler::handleRead() {
      loop_->assertInLoopThread();

      uint64_t count = readEventfd(eventfd_);
      if (count > 0) {
        spdlog::trace("EventfdHandler received count: {}", count);

        if (notifyCallback_) {
          try {
            notifyCallback_();
          } catch (const std::exception& e) {
            spdlog::error("Exception in eventfd notify callback: {}", e.what());
          } catch (...) {
            spdlog::error("Unknown exception in eventfd notify callback");
          }
        } else {
          spdlog::warn("EventfdHandler received event but no callback set");
        }
      }
    }

  }  // namespace event
}  // namespace zexuan
