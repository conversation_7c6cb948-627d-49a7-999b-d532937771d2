#include "zexuan/event/sys/inotify_handler.hpp"

#include <spdlog/spdlog.h>
#include <sys/inotify.h>
#include <unistd.h>

#include <cerrno>
#include <cstring>
#include <sstream>

#include "zexuan/event/channel.hpp"
#include "zexuan/event/event_loop.hpp"

namespace zexuan {
  namespace event {

    namespace {
      int createInotifyfd() {
        int ifd = ::inotify_init1(IN_NONBLOCK | IN_CLOEXEC);
        if (ifd < 0) {
          spdlog::error("Failed to create inotify fd: {}", strerror(errno));
          abort();
        }
        spdlog::debug("Created inotify fd: {}", ifd);
        return ifd;
      }
    }  // namespace

    // InotifyWatch静态成员定义
    std::atomic<int64_t> InotifyHandler::InotifyWatch::s_numCreated_{0};

    InotifyHandler::InotifyHandler(EventLoop* loop)
        : loop_(loop),
          inotifyfd_(createInotifyfd()),
          inotifyChannel_(std::make_unique<Channel>(loop, inotifyfd_)) {
      loop_->assertInLoopThread();

      inotifyChannel_->setReadCallback([this](Timestamp) { handleRead(); });

      spdlog::debug("InotifyHandler created with inotify fd: {}", inotifyfd_);
    }

    InotifyHandler::~InotifyHandler() {
      spdlog::debug("InotifyHandler destructor");

      if (inotifyChannel_) {
        inotifyChannel_->disableAll();
        inotifyChannel_->remove();
      }

      // 清理所有监听
      for (const auto& pair : watches_) {
        ::inotify_rm_watch(inotifyfd_, pair.first);
      }

      if (inotifyfd_ >= 0) {
        ::close(inotifyfd_);
      }
    }

    WatchId InotifyHandler::addWatch(const std::string& path, uint32_t mask, FileEventCallback cb) {
      WatchId watchId;
      loop_->runInLoop([this, path, mask, cb = std::move(cb), &watchId]() mutable {
        watchId = addWatchInLoop(path, mask, std::move(cb));
      });
      return watchId;
    }

    WatchId InotifyHandler::watchFile(const std::string& path, FileEventCallback cb) {
      // 监听文件的修改、删除、移动等事件
      uint32_t mask = IN_MODIFY | IN_DELETE_SELF | IN_MOVE_SELF | IN_CLOSE_WRITE;
      return addWatch(path, mask, std::move(cb));
    }

    WatchId InotifyHandler::watchDirectory(const std::string& path, FileEventCallback cb) {
      // 监听目录中文件的创建、删除、移动等事件
      uint32_t mask = IN_CREATE | IN_DELETE | IN_MOVED_FROM | IN_MOVED_TO | IN_CLOSE_WRITE;
      return addWatch(path, mask, std::move(cb));
    }

    void InotifyHandler::removeWatch(WatchId watchId) {
      loop_->runInLoop([this, watchId]() { removeWatchInLoop(watchId); });
    }

    WatchId InotifyHandler::addWatchInLoop(const std::string& path, uint32_t mask,
                                           FileEventCallback cb) {
      loop_->assertInLoopThread();

      int wd = ::inotify_add_watch(inotifyfd_, path.c_str(), mask);
      if (wd < 0) {
        spdlog::error("Failed to add inotify watch for {}: {}", path, strerror(errno));
        return WatchId();
      }

      auto watch = std::make_unique<InotifyWatch>(path, mask, std::move(cb), wd);
      InotifyWatch* watchPtr = watch.get();
      WatchId watchId(static_cast<void*>(watchPtr), watchPtr->sequence());

      // 存储watch对象
      watchStorage_.push_back(std::move(watch));
      watches_[wd] = watchPtr;

      // 如果这是第一个监听，启用读取事件
      if (watches_.size() == 1) {
        inotifyChannel_->enableReading();
      }

      spdlog::debug("Added inotify watch: wd={}, path={}, mask=0x{:x}", wd, path, mask);
      return watchId;
    }

    void InotifyHandler::removeWatchInLoop(WatchId watchId) {
      loop_->assertInLoopThread();

      if (!watchId.valid()) {
        return;
      }

      InotifyWatch* watch = static_cast<InotifyWatch*>(watchId.watch_);
      int wd = watch->wd();

      auto it = watches_.find(wd);
      if (it != watches_.end()) {
        if (::inotify_rm_watch(inotifyfd_, wd) < 0) {
          spdlog::error("Failed to remove inotify watch {}: {}", wd, strerror(errno));
        } else {
          spdlog::debug("Removed inotify watch: wd={}, path={}", wd, watch->path());
        }
        watches_.erase(it);
      }

      // 如果没有监听了，禁用读取事件
      if (watches_.empty()) {
        inotifyChannel_->disableReading();
      }
    }

    void InotifyHandler::removeAllWatchesInLoop() {
      loop_->assertInLoopThread();

      for (const auto& pair : watches_) {
        if (::inotify_rm_watch(inotifyfd_, pair.first) < 0) {
          spdlog::error("Failed to remove inotify watch {}: {}", pair.first, strerror(errno));
        }
      }

      watches_.clear();
      inotifyChannel_->disableReading();

      spdlog::debug("Removed all inotify watches");
    }

    void InotifyHandler::handleRead() {
      loop_->assertInLoopThread();

      char buffer[4096];
      ssize_t length = ::read(inotifyfd_, buffer, sizeof(buffer));

      if (length < 0) {
        if (errno != EAGAIN && errno != EWOULDBLOCK) {
          spdlog::error("Failed to read inotify fd: {}", strerror(errno));
        }
        return;
      }

      ssize_t i = 0;
      while (i < length) {
        struct inotify_event* event = reinterpret_cast<struct inotify_event*>(&buffer[i]);

        auto it = watches_.find(event->wd);
        if (it != watches_.end()) {
          InotifyWatch* watch = it->second;
          std::string name = event->len > 0 ? std::string(event->name) : "";

          spdlog::trace("Inotify event: path={}, mask=0x{:x} ({}), name={}", watch->path(),
                        event->mask, maskToString(event->mask), name);

          try {
            watch->run(event->mask, name);
          } catch (const std::exception& e) {
            spdlog::error("Exception in inotify callback for {}: {}", watch->path(), e.what());
          } catch (...) {
            spdlog::error("Unknown exception in inotify callback for {}", watch->path());
          }
        } else {
          spdlog::warn("Received inotify event for unknown watch descriptor: {}", event->wd);
        }

        i += sizeof(struct inotify_event) + event->len;
      }
    }

    std::string InotifyHandler::maskToString(uint32_t mask) {
      std::ostringstream oss;
      bool first = true;

      auto addFlag = [&](uint32_t flag, const char* name) {
        if (mask & flag) {
          if (!first) oss << "|";
          oss << name;
          first = false;
        }
      };

      addFlag(IN_ACCESS, "ACCESS");
      addFlag(IN_MODIFY, "MODIFY");
      addFlag(IN_ATTRIB, "ATTRIB");
      addFlag(IN_CLOSE_WRITE, "CLOSE_WRITE");
      addFlag(IN_CLOSE_NOWRITE, "CLOSE_NOWRITE");
      addFlag(IN_OPEN, "OPEN");
      addFlag(IN_MOVED_FROM, "MOVED_FROM");
      addFlag(IN_MOVED_TO, "MOVED_TO");
      addFlag(IN_CREATE, "CREATE");
      addFlag(IN_DELETE, "DELETE");
      addFlag(IN_DELETE_SELF, "DELETE_SELF");
      addFlag(IN_MOVE_SELF, "MOVE_SELF");
      addFlag(IN_UNMOUNT, "UNMOUNT");
      addFlag(IN_Q_OVERFLOW, "Q_OVERFLOW");
      addFlag(IN_IGNORED, "IGNORED");
      addFlag(IN_ISDIR, "ISDIR");

      return oss.str();
    }

  }  // namespace event
}  // namespace zexuan
