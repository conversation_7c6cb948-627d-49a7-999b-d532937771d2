#include "zexuan/event/sys/signal_handler.hpp"

#include <spdlog/spdlog.h>
#include <sys/signalfd.h>
#include <unistd.h>

#include <cerrno>
#include <cstring>

#include "zexuan/event/channel.hpp"
#include "zexuan/event/event_loop.hpp"

using namespace zexuan;
using namespace zexuan::event;

namespace {
  // 创建 signalfd
  int createSignalfd(const sigset_t* mask) {
    int sfd = ::signalfd(-1, mask, SFD_NONBLOCK | SFD_CLOEXEC);
    if (sfd < 0) {
      spdlog::critical("Failed to create signalfd: {}", strerror(errno));
      abort();
    }
    return sfd;
  }

  // 阻塞信号的默认处理
  void blockSignals(const sigset_t* mask) {
    if (::pthread_sigmask(SIG_BLOCK, mask, nullptr) != 0) {
      spdlog::critical("Failed to block signals: {}", strerror(errno));
      abort();
    }
  }
}  // namespace

SignalHandler::SignalHandler(EventLoop* loop)
    : loop_(loop), signalfd_(-1), signalChannel_(nullptr) {
  loop_->assertInLoopThread();

  // 初始化信号掩码
  sigemptyset(&signalMask_);

  // 创建初始的 signalfd（空掩码）
  signalfd_ = createSignalfd(&signalMask_);

  // 创建 Channel 并设置回调
  signalChannel_ = std::make_unique<Channel>(loop_, signalfd_);
  signalChannel_->setReadCallback([this](Timestamp) { handleRead(); });

  spdlog::debug("SignalHandler created with signalfd: {}", signalfd_);
}

SignalHandler::~SignalHandler() {
  spdlog::debug("SignalHandler destructor");

  if (signalChannel_) {
    signalChannel_->disableAll();
    signalChannel_->remove();
  }

  if (signalfd_ >= 0) {
    ::close(signalfd_);
  }
}

void SignalHandler::registerSignal(int signal, SignalCallback cb) {
  loop_->runInLoop([this, signal, cb = std::move(cb)]() mutable {
    registerSignalInLoop(signal, std::move(cb));
  });
}

void SignalHandler::unregisterSignal(int signal) {
  loop_->runInLoop([this, signal]() { unregisterSignalInLoop(signal); });
}

void SignalHandler::registerSignalInLoop(int signal, SignalCallback cb) {
  loop_->assertInLoopThread();

  spdlog::debug("Registering signal: {}", signal);

  signalCallbacks_[signal] = std::move(cb);

  // 更新信号掩码
  sigaddset(&signalMask_, signal);
  updateSignalMask();
}

void SignalHandler::unregisterSignalInLoop(int signal) {
  loop_->assertInLoopThread();

  spdlog::debug("Unregistering signal: {}", signal);

  auto it = signalCallbacks_.find(signal);
  if (it != signalCallbacks_.end()) {
    signalCallbacks_.erase(it);

    // 从信号掩码中移除
    sigdelset(&signalMask_, signal);
    updateSignalMask();
  }
}

void SignalHandler::updateSignalMask() {
  // 阻塞信号的默认处理
  blockSignals(&signalMask_);

  // 更新 signalfd
  if (::signalfd(signalfd_, &signalMask_, SFD_NONBLOCK | SFD_CLOEXEC) < 0) {
    spdlog::error("Failed to update signalfd: {}", strerror(errno));
    return;
  }

  // 如果有信号注册，启用读取；否则禁用
  if (!signalCallbacks_.empty()) {
    if (!signalChannel_->isReading()) {
      signalChannel_->enableReading();
    }
  } else {
    if (signalChannel_->isReading()) {
      signalChannel_->disableReading();
    }
  }
}

void SignalHandler::handleRead() {
  struct signalfd_siginfo si;

  while (true) {
    ssize_t s = ::read(signalfd_, &si, sizeof(si));

    if (s == sizeof(si)) {
      int signal = si.ssi_signo;
      spdlog::debug("Received signal: {}", signal);

      auto it = signalCallbacks_.find(signal);
      if (it != signalCallbacks_.end()) {
        try {
          it->second(signal);  // 执行回调
        } catch (const std::exception& e) {
          spdlog::error("Exception in signal callback for signal {}: {}", signal, e.what());
        } catch (...) {
          spdlog::error("Unknown exception in signal callback for signal {}", signal);
        }
      } else {
        spdlog::warn("No sys for signal: {}", signal);
      }
    } else if (s < 0) {
      if (errno == EAGAIN || errno == EWOULDBLOCK) {
        break;  // 没有更多信号
      } else {
        spdlog::error("Failed to read signalfd: {}", strerror(errno));
        break;
      }
    } else {
      spdlog::error("Partial read from signalfd: {} bytes", s);
      break;
    }
  }
}

void SignalHandler::registerShutdownSignals(SignalCallback cb) {
  registerSignal(SIGINT, cb);
  registerSignal(SIGTERM, cb);
}

void SignalHandler::registerUserSignals(SignalCallback cb) {
  registerSignal(SIGUSR1, cb);
  registerSignal(SIGUSR2, cb);
}
