/**
 * @file protocol_client.cpp
 * @brief 简化的IEC 60870-5-103协议客户端实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/client/protocol_client.hpp"

// 包含factory和handlers接口
#include <spdlog/spdlog.h>
#include <fstream>
#include <nlohmann/json.hpp>

#include <chrono>
#include <iomanip>
#include <sstream>

#include "zexuan/protocol/protocol_client/client/message_handler_factory.hpp"
#include "zexuan/protocol/protocol_client/client/message_handler_interface.hpp"

namespace zexuan {
  namespace protocol {
    namespace client {

      ProtocolClient::ProtocolClient(event::EventLoop* event_loop, const std::string& config_file_path)
          : event_loop_(event_loop),
            config_file_path_(config_file_path),
            tcp_client_(nullptr) {
        // 加载配置
        if (!LoadConfiguration()) {
            throw std::runtime_error("Failed to load configuration from: " + config_file_path);
        }
        
        // 初始化handler factory
        InitializeHandlers();

        spdlog::info("ProtocolClient: Created for {}:{}", server_host_, server_port_);
      }

      ProtocolClient::~ProtocolClient() {
        if (is_running_.load()) {
          auto result = Stop();
          // 在析构函数中不使用日志记录，避免heap-use-after-free
        }
      }

      base::Result<void> ProtocolClient::Start() {
        if (is_running_.load()) {
          spdlog::warn("ProtocolClient: Already running");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        if (!event_loop_) {
          spdlog::error("ProtocolClient: Event loop not provided");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        try {
          // 创建服务器地址
          event::Address server_addr(server_host_, server_port_);

          // 创建TCP客户端
          tcp_client_
              = std::make_unique<event::TcpClient>(event_loop_, server_addr, "ProtocolClient");

          // 设置回调
          tcp_client_->setConnectionCallback(
              [this](const event::TcpConnectionPtr& conn) { OnConnection(conn); });

          tcp_client_->setMessageCallback(
              [this](const event::TcpConnectionPtr& conn, event::Buffer* buf,
                     std::chrono::system_clock::time_point receive_time) {
                OnMessage(conn, buf, receive_time);
              });

          tcp_client_->setWriteCompleteCallback(
              [this](const event::TcpConnectionPtr& conn) { OnWriteComplete(conn); });

          is_running_.store(true);
          spdlog::info("ProtocolClient: Started successfully");
          return {};

        } catch (const std::exception& e) {
          spdlog::error("ProtocolClient: Start failed: {}", e.what());
          return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }
      }

      base::Result<void> ProtocolClient::Stop() {
        if (!is_running_.load()) {
          spdlog::warn("ProtocolClient: Not running");
          return {};
        }

        try {
          // 断开连接
          Disconnect();

          // 停止TCP客户端
          if (tcp_client_) {
            tcp_client_.reset();
          }

          is_running_.store(false);
          spdlog::info("ProtocolClient: Stopped successfully");
          return {};

        } catch (const std::exception& e) {
          spdlog::error("ProtocolClient: Stop failed: {}", e.what());
          return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }
      }

      base::Result<void> ProtocolClient::Connect() {
        if (!is_running_.load()) {
          spdlog::error("ProtocolClient: Not started");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        if (is_connected_.load()) {
          spdlog::warn("ProtocolClient: Already connected");
          return {};
        }

        if (!tcp_client_) {
          spdlog::error("ProtocolClient: TCP client not initialized");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        try {
          tcp_client_->connect();
          spdlog::info("ProtocolClient: Connection initiated to {}:{}", server_host_, server_port_);
          return {};

        } catch (const std::exception& e) {
          spdlog::error("ProtocolClient: Connect failed: {}", e.what());
          return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }
      }

      void ProtocolClient::Disconnect() {
        if (connection_) {
          connection_->shutdown();
          connection_.reset();
        }
        is_connected_.store(false);
      }

      bool ProtocolClient::IsConnected() const { return is_connected_.load(); }

      base::Result<void> ProtocolClient::SendType1Message(uint8_t target,
                                                          const std::string& test_data) {
        return SendMessageViaHandler(1, target, test_data);
      }

      base::Result<void> ProtocolClient::SendType2Message(uint8_t target,
                                                          const std::string& test_data) {
        return SendMessageViaHandler(2, target, test_data);
      }

      base::Result<void> ProtocolClient::SendType3Message(uint8_t target) {
        return SendMessageViaHandler(3, target);
      }

      base::Result<void> ProtocolClient::SendType4Message(uint8_t target) {
        return SendMessageViaHandler(4, target);
      }

      base::Result<void> ProtocolClient::SendType5Message(uint8_t target,
                                                          const std::string& new_name) {
        return SendMessageViaHandler(5, target, new_name);
      }

      void ProtocolClient::SetConnectionCallback(ConnectionCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        connection_callback_ = std::move(callback);
      }

      void ProtocolClient::SetMessageCallback(MessageCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        message_callback_ = std::move(callback);
      }

      // 私有方法实现
      void ProtocolClient::OnConnection(const event::TcpConnectionPtr& conn) {
        if (conn->connected()) {
          connection_ = conn;
          is_connected_.store(true);
          spdlog::info("ProtocolClient: Connected to {}:{}", server_host_, server_port_);
          NotifyConnectionStatus(true);
        } else {
          connection_.reset();
          is_connected_.store(false);
          spdlog::info("ProtocolClient: Disconnected from {}:{}", server_host_, server_port_);
          NotifyConnectionStatus(false);
        }
      }

      void ProtocolClient::OnMessage(const event::TcpConnectionPtr& conn, event::Buffer* buf,
                                     std::chrono::system_clock::time_point receive_time) {
        std::string data = buf->retrieveAllAsString();
        spdlog::debug("ProtocolClient: Received {} bytes", data.size());
        ProcessReceivedData(data);
      }

      void ProtocolClient::OnWriteComplete(const event::TcpConnectionPtr& conn) {
        spdlog::debug("ProtocolClient: Write completed");
      }

      void ProtocolClient::InitializeHandlers() {
        // 创建handler factory
        handler_factory_ = std::make_unique<MessageTypeHandlerFactory>();

        spdlog::info("ProtocolClient: Initialized message handler factory");
      }

      IMessageTypeHandler* ProtocolClient::GetHandler(uint8_t message_type) {
        if (!handler_factory_) {
          return nullptr;
        }
        return handler_factory_->GetHandler(message_type);
      }

      void ProtocolClient::NotifyConnectionStatus(bool connected) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (connection_callback_) {
          connection_callback_(connected);
        }
      }

      void ProtocolClient::NotifyMessageReceived(const base::Message& message) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (message_callback_) {
          message_callback_(message);
        }
      }

      base::Result<void> ProtocolClient::SendMessageViaHandler(
          uint8_t message_type, uint8_t target, const std::string& text_content,
          const std::vector<uint8_t>& variable_data) {
        if (!is_connected_.load()) {
          spdlog::error("ProtocolClient: Not connected");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        // 获取对应的handler
        auto* handler = GetHandler(message_type);
        if (!handler) {
          spdlog::error("ProtocolClient: No handler for message type {}", message_type);
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        // 创建输入消息对象
        base::Message input_message;
        input_message.setTyp(message_type);  // 设置消息类型
        input_message.setTarget(target);
        if (!text_content.empty()) {
          input_message.setTextContent(text_content);
        }
        if (!variable_data.empty()) {
          input_message.setVariableStructure(variable_data);
        }

        // 通过handler创建消息
        auto message_result = handler->CreateMessage(input_message);
        if (!message_result) {
          spdlog::error("ProtocolClient: Failed to create message type {}", message_type);
          return std::unexpected(message_result.error());
        }

        // 序列化消息
        std::vector<uint8_t> serialized_data;
        size_t serialized_size = message_result.value().serialize(serialized_data);

        if (serialized_size == 0) {
          spdlog::error("ProtocolClient: Failed to serialize message type {}", message_type);
          return std::unexpected(base::ErrorCode::OPERATION_FAILED);
        }

        // 发送数据
        if (!connection_) {
          spdlog::error("ProtocolClient: No active connection");
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        connection_->send(serialized_data.data(), serialized_size);
        spdlog::info("ProtocolClient: Sent message type {} to target {}", message_type, target);

        return {};
      }

      void ProtocolClient::ProcessReceivedData(const std::string& data) {
        std::lock_guard<std::mutex> lock(buffer_mutex_);
        spdlog::debug("ProtocolClient: Received {} bytes", data.size());
        receive_buffer_ += data;

        // 简化的帧解析 - 寻找完整的IEC103消息
        while (receive_buffer_.size() >= 6) {  // 最小消息长度
          // 寻找起始字符
          size_t start_pos = receive_buffer_.find('\x68');
          if (start_pos == std::string::npos) {
            // 打印缓冲区内容用于调试
            std::string hex_dump;
            for (size_t i = 0; i < std::min(receive_buffer_.size(), size_t(32)); ++i) {
              char hex_char[4];
              snprintf(hex_char, sizeof(hex_char), "%02X ", static_cast<uint8_t>(receive_buffer_[i]));
              hex_dump += hex_char;
            }
            spdlog::debug("ProtocolClient: No start character found, buffer content (first 32 bytes): {}", hex_dump);
            spdlog::debug("ProtocolClient: Clearing buffer of {} bytes", receive_buffer_.size());
            receive_buffer_.clear();
            break;
          }

          if (start_pos > 0) {
            spdlog::debug("ProtocolClient: Skipping {} bytes before start character", start_pos);
            receive_buffer_.erase(0, start_pos);
          }

          if (receive_buffer_.size() < 6) {
            spdlog::debug("ProtocolClient: Buffer too small after finding start, size={}", receive_buffer_.size());
            break;
          }

          // 获取长度字段
          uint8_t length_l = static_cast<uint8_t>(receive_buffer_[1]);
          uint8_t length_h = static_cast<uint8_t>(receive_buffer_[2]);
          size_t asdu_length = length_l | (static_cast<size_t>(length_h) << 8);
          size_t total_length = asdu_length + 3;  // ASDU长度 + 协议头(START + LENGTH_L + LENGTH_H)

          // 打印协议头的详细信息
          std::string header_hex;
          for (size_t i = 0; i < std::min(receive_buffer_.size(), size_t(10)); ++i) {
            char hex_char[4];
            snprintf(hex_char, sizeof(hex_char), "%02X ", static_cast<uint8_t>(receive_buffer_[i]));
            header_hex += hex_char;
          }

          spdlog::debug("ProtocolClient: Header bytes: {}", header_hex);
          spdlog::debug("ProtocolClient: LENGTH_L={}, LENGTH_H={}, ASDU_length={}, total_length={}, buffer_size={}",
                        length_l, length_h, asdu_length, total_length, receive_buffer_.size());

          if (receive_buffer_.size() < total_length) {
            spdlog::debug("ProtocolClient: Incomplete frame, waiting for more data");
            break;  // 数据不完整
          }

          // 提取完整帧
          std::vector<uint8_t> frame_data(receive_buffer_.begin(),
                                          receive_buffer_.begin() + total_length);
          receive_buffer_.erase(0, total_length);

          spdlog::debug("ProtocolClient: Extracted frame of {} bytes, remaining buffer size={}",
                        frame_data.size(), receive_buffer_.size());

          // 解析消息
          base::Message message;
          if (ParseMessage(frame_data, message)) {
            ProcessParsedMessage(message);
          } else {
            spdlog::warn("ProtocolClient: Failed to parse frame of {} bytes", frame_data.size());
          }
        }
      }

      bool ProtocolClient::ParseMessage(const std::vector<uint8_t>& frame_data,
                                        base::Message& message) {
        // 使用base::Message的deserialize方法
        size_t parsed = message.deserialize(frame_data);

        if (parsed > 0) {
          spdlog::debug(
              "ProtocolClient: Parsed IEC103 message: TYP={:02X}, VSQ={:02X}, COT={:02X}, "
              "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
              message.getTyp(), message.getVsq(), message.getCot(), message.getSource(),
              message.getTarget(), message.getFun(), message.getInf());
          return true;
        } else {
          spdlog::warn("ProtocolClient: Failed to parse message frame");
          return false;
        }
      }

      void ProtocolClient::ProcessParsedMessage(const base::Message& message) {
        uint8_t message_type = message.getTyp();
        uint8_t cot = message.getCot();

        // 检查是否为多帧响应（Type2）
        if (message_type == 2) {
          // Type2 多帧响应处理
          std::string cache_key = std::to_string(message.getSource()) + "_" +
                                  std::to_string(message.getTarget()) + "_" +
                                  std::to_string(message.getFun()) + "_" +
                                  std::to_string(message.getInf());

          std::lock_guard<std::mutex> lock(multi_frame_cache_mutex_);

          // 添加到缓存
          auto& cache_entry = multi_frame_cache_[cache_key];
          cache_entry.frames.push_back(message);

          spdlog::debug("ProtocolClient: Added Type2 frame to cache, key={}, frame_count={}, COT={:02X}",
                        cache_key, cache_entry.frames.size(), cot);

          // 检查是否为最后一帧（COT=0x0A 表示终止激活确认）
          if (cot == 0x0A) {
            // 这是最后一帧，处理所有缓存的帧
            spdlog::info("ProtocolClient: Received complete Type2 multi-frame response, processing {} frames",
                         cache_entry.frames.size());

            // 按顺序通知所有帧
            for (const auto& cached_frame : cache_entry.frames) {
              NotifyMessageReceived(cached_frame);
            }

            // 清理缓存
            multi_frame_cache_.erase(cache_key);
          } else {
            // 不是最后一帧，继续等待
            spdlog::debug("ProtocolClient: Waiting for more Type2 frames, current count={}",
                          cache_entry.frames.size());
          }
        } else {
          // 单帧消息，直接处理
          NotifyMessageReceived(message);
        }

        // 清理超时的多帧缓存
        CleanupExpiredMultiFrameCache();
      }

      void ProtocolClient::CleanupExpiredMultiFrameCache() {
        auto now = std::chrono::system_clock::now();
        auto it = multi_frame_cache_.begin();

        while (it != multi_frame_cache_.end()) {
          auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
              now - it->second.first_frame_time);

          if (elapsed >= MULTI_FRAME_TIMEOUT) {
            spdlog::warn("ProtocolClient: Multi-frame response timeout, discarding {} frames for key={}",
                         it->second.frames.size(), it->first);
            it = multi_frame_cache_.erase(it);
          } else {
            ++it;
          }
        }
      }

      bool ProtocolClient::LoadConfiguration() {
        std::ifstream config_file(config_file_path_);
        if (!config_file.is_open()) {
          spdlog::error("ProtocolClient: Cannot open config file: {}", config_file_path_);
          return false;
        }

        nlohmann::json config;
        try {
          config_file >> config;
        } catch (const std::exception& e) {
          spdlog::error("ProtocolClient: Failed to parse config file {}: {}", config_file_path_, e.what());
          return false;
        }

        // 读取配置段
        if (!config.contains("protocol") || !config["protocol"].contains("client")) {
          spdlog::error("ProtocolClient: Missing protocol.client section in config file: {}", config_file_path_);
          return false;
        }

        auto client_config = config["protocol"]["client"];
        
        // 读取必需的配置项
        if (!client_config.contains("server_host")) {
          spdlog::error("ProtocolClient: Missing required config: protocol.client.server_host");
          return false;
        }
        
        if (!client_config.contains("server_port")) {
          spdlog::error("ProtocolClient: Missing required config: protocol.client.server_port");
          return false;
        }
        
        // 提取配置值
        server_host_ = client_config["server_host"];
        server_port_ = client_config["server_port"];
        
        spdlog::info("ProtocolClient: Loaded configuration - server_host={}, server_port={}", 
                    server_host_, server_port_);
        
        return true;
      }

      std::string ProtocolClient::GetMessageTextContent(const base::Message& message) {
        uint8_t message_type = message.getTyp();
        const std::vector<uint8_t>& variable_data = message.getVariableStructure();
        
        // 根据消息类型解析文本内容
        switch (message_type) {
          case 1: // Type 1: 单帧测试消息 - 直接返回文本内容
          case 2: // Type 2: 多帧测试消息 - 直接返回文本内容
          case 5: // Type 5: 重命名消息 - 直接返回文本内容
            return message.getTextContent();
            
          case 3: // Type 3: 时间戳事件消息 - 解析时间戳
            return ParseType3MessageContent(variable_data);
            
          case 4: // Type 4: 本地状态消息 - 解析状态信息
            return ParseType4MessageContent(variable_data);
            
          default:
            // 未知消息类型，尝试返回可打印字符
            return ExtractPrintableText(variable_data);
        }
      }
      
      std::string ProtocolClient::ParseType3MessageContent(const std::vector<uint8_t>& variable_data) {
        if (variable_data.size() < 7) {
          return "Invalid timestamp data";
        }
        
        // 解析IEC 60870-5-103时间格式（7字节）
        uint16_t milliseconds = variable_data[0] | (static_cast<uint16_t>(variable_data[1]) << 8);
        uint8_t minute = variable_data[2];
        uint8_t hour = variable_data[3];
        uint8_t day = variable_data[4];
        uint8_t month = variable_data[5];
        uint8_t year = variable_data[6];
        
        std::ostringstream oss;
        oss << "Timestamp: 20" << std::setfill('0') << std::setw(2) << static_cast<int>(year) << "-"
            << std::setfill('0') << std::setw(2) << static_cast<int>(month) << "-"
            << std::setfill('0') << std::setw(2) << static_cast<int>(day) << " "
            << std::setfill('0') << std::setw(2) << static_cast<int>(hour) << ":"
            << std::setfill('0') << std::setw(2) << static_cast<int>(minute) << "."
            << std::setfill('0') << std::setw(3) << static_cast<int>(milliseconds);
        
        // 如果有额外的文本数据（在时间戳之后），追加显示
        if (variable_data.size() > 7) {
          std::string additional_text = ExtractPrintableText(
              std::vector<uint8_t>(variable_data.begin() + 7, variable_data.end()));
          if (!additional_text.empty()) {
            oss << " - " << additional_text;
          }
        }
        
        return oss.str();
      }
      
      std::string ProtocolClient::ParseType4MessageContent(const std::vector<uint8_t>& variable_data) {
        if (variable_data.size() < 11) {
          return "Invalid status data";
        }
        
        std::ostringstream oss;
        
        // 解析时间戳（前7字节）
        if (variable_data.size() >= 7) {
          uint16_t milliseconds = variable_data[0] | (static_cast<uint16_t>(variable_data[1]) << 8);
          uint8_t minute = variable_data[2];
          uint8_t hour = variable_data[3];
          uint8_t day = variable_data[4];
          uint8_t month = variable_data[5];
          uint8_t year = variable_data[6];
          
          oss << "Status at: 20" << std::setfill('0') << std::setw(2) << static_cast<int>(year) << "-"
              << std::setfill('0') << std::setw(2) << static_cast<int>(month) << "-"
              << std::setfill('0') << std::setw(2) << static_cast<int>(day) << " "
              << std::setfill('0') << std::setw(2) << static_cast<int>(hour) << ":"
              << std::setfill('0') << std::setw(2) << static_cast<int>(minute) << "."
              << std::setfill('0') << std::setw(3) << static_cast<int>(milliseconds);
        }
        
        // 解析状态数据
        if (variable_data.size() >= 11) {
          uint8_t device_status = variable_data[7];
          uint8_t comm_status = variable_data[8];
          uint8_t run_mode = variable_data[9];
          uint8_t alarm_status = variable_data[10];
          
          oss << " - Device:" << (device_status == 0x01 ? "Online" : "Offline")
              << " Comm:" << (comm_status == 0x02 ? "Normal" : "Error")
              << " Mode:" << (run_mode == 0x03 ? "Auto" : "Manual")
              << " Alarm:" << (alarm_status == 0x00 ? "None" : "Active");
        }
        
        // 解析负载数据（如果有的话）
        if (variable_data.size() >= 15) {
          uint32_t load_percent = variable_data[11] | (static_cast<uint32_t>(variable_data[12]) << 8)
                                  | (static_cast<uint32_t>(variable_data[13]) << 16)
                                  | (static_cast<uint32_t>(variable_data[14]) << 24);
          oss << " Load:" << load_percent << "%";
        }
        
        return oss.str();
      }
      
      std::string ProtocolClient::ExtractPrintableText(const std::vector<uint8_t>& data) {
        std::string result;
        result.reserve(data.size());
        
        for (uint8_t byte : data) {
          // 只包含可打印ASCII字符 (32-126)
          if (byte >= 32 && byte <= 126) {
            result.push_back(static_cast<char>(byte));
          }
          // 其他字符替换为空格
          else if (result.empty() || result.back() != ' ') {
            result.push_back(' ');
          }
        }
        
        // 去除首尾空格
        size_t start = result.find_first_not_of(' ');
        if (start == std::string::npos) {
          return "";
        }
        size_t end = result.find_last_not_of(' ');
        return result.substr(start, end - start + 1);
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan
