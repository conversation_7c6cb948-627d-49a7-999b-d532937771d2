/**
 * @file type1_handler.cpp
 * @brief Type 1 单帧测试消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type1_handler.hpp"

#include <spdlog/spdlog.h>

namespace zexuan {
  namespace protocol {
    namespace client {

      // 静态常量定义
      const std::string Type1Handler::DEFAULT_TEST_DATA = "SINGLE_FRAME_TEST_DATA";

      Type1Handler::Type1Handler() {
        spdlog::debug("Type1Handler: Created for single frame test messages");
      }

      bool Type1Handler::ValidateRequest(const base::Message& message) const {
        if (message.getTyp() != 1) {
          return false;
        }

        // 检查目标地址是否合理
        if (message.getTarget() == 0) {
          spdlog::debug("Type1Handler: Target address should not be 0");
          return false;
        }

        // VSQ应该是单信息类型
        if (message.getVsq() != DEFAULT_VSQ && message.getVsq() != 0x01) {
          spdlog::debug("Type1Handler: VSQ should be single info type, got {:02X}",
                        message.getVsq());
          // 这不是错误，只是警告
        }

        // 如果没有提供测试数据，将使用默认数据
        if (message.getTextContent().empty() && message.getVariableStructure().empty()) {
          spdlog::debug("Type1Handler: No test data provided, will use default");
        }

        return true;
      }

      base::Result<base::Message> Type1Handler::CreateMessage(
          const base::Message& input_message) const {
        if (input_message.getTyp() != 1) {
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("Type1Handler: Creating Type 1 single frame test message");

        // 创建输出消息对象，从输入消息复制参数
        base::Message message = input_message;

        // 设置Type 1的默认值（如果未设置）
        message.setTyp(1);

        if (message.getVsq() == 0x00) {
          message.setVsq(DEFAULT_VSQ);
        }
        if (message.getCot() == 0x00) {
          message.setCot(DEFAULT_COT);
        }
        if (message.getFun() == 0x00) {
          message.setFun(DEFAULT_FUN);
        }
        if (message.getInf() == 0x00) {
          message.setInf(DEFAULT_INF);
        }

        // 设置测试数据（如果没有提供）
        if (message.getTextContent().empty() && message.getVariableStructure().empty()) {
          message.setTextContent(DEFAULT_TEST_DATA);
        }

        spdlog::info("Type1Handler: Created single frame test message to target {}",
                     message.getTarget());
        spdlog::debug(
            "Type1Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
            message.getVsq(), message.getCot(), message.getFun(), message.getInf());

        return message;
      }

      base::Result<MessageResult> Type1Handler::ProcessResponse(
          const base::Message& response) const {
        spdlog::debug("Type1Handler: Processing Type 1 response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();

        // 打印响应详情
        spdlog::info(
            "Type1Handler: Received response - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
            "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
            response.getTyp(), response.getVsq(), response.getCot(), response.getSource(),
            response.getTarget(), response.getFun(), response.getInf());

        // 提取响应内容
        std::string response_content = response.getTextContent();
        std::vector<uint8_t> response_data = response.getVariableStructure();

        MessageResult result;
        result.success = true;

        // 分析响应类型和传送原因
        if (response_type == 1) {
          // Type 1 响应
          if (response_cot == 0x07) {  // 激活确认
            result.description = "Single frame test confirmed";
            spdlog::info("Type1Handler: Single frame test confirmed by server");
          } else if (response_cot == 0x08) {  // 停止激活确认
            result.description = "Single frame test stopped";
            spdlog::info("Type1Handler: Single frame test stopped by server");
          } else {
            result.description
                = "Single frame test response with COT " + std::to_string(response_cot);
            spdlog::info("Type1Handler: Single frame test response with COT {:02X}", response_cot);
          }
        } else {
          // 其他类型的响应
          result.description = "Unexpected response type " + std::to_string(response_type);
          spdlog::warn("Type1Handler: Unexpected response type {} for Type 1 request",
                       response_type);
        }

        // 如果有文本内容，添加到结果中
        if (!response_content.empty()) {
          result.description += " - Content: " + response_content;
          spdlog::debug("Type1Handler: Response content: {}", response_content);
        }

        // 如果有可变结构体数据，保存到结果中
        if (!response_data.empty()) {
          result.data = response_data;
          spdlog::debug("Type1Handler: Response has {} bytes of variable data",
                        response_data.size());
        }

        return result;
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan
