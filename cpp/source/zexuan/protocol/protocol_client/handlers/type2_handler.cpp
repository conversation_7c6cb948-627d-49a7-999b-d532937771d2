/**
 * @file type2_handler.cpp
 * @brief Type 2 多帧测试消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type2_handler.hpp"

#include <spdlog/spdlog.h>

namespace zexuan {
  namespace protocol {
    namespace client {

      // 静态常量定义
      const std::string Type2Handler::DEFAULT_TEST_DATA = "MULTI_FRAME_TEST_DATA";

      Type2Handler::Type2Handler() {
        spdlog::debug("Type2Handler: Created for multi frame test messages");
      }

      bool Type2Handler::ValidateRequest(const base::Message& message) const {
        if (message.getTyp() != 2) {
          return false;
        }

        // 检查目标地址是否合理
        if (message.getTarget() == 0) {
          spdlog::debug("Type2Handler: Target address should not be 0");
          return false;
        }

        // VSQ应该是多信息类型
        if (message.getVsq() != DEFAULT_VSQ && message.getVsq() != 0x01) {
          spdlog::debug("Type2Handler: VSQ should be multi info type, got {:02X}",
                        message.getVsq());
          // 这不是错误，只是警告
        }

        // 如果没有提供测试数据，将使用默认数据
        if (message.getTextContent().empty() && message.getVariableStructure().empty()) {
          spdlog::debug("Type2Handler: No test data provided, will use default");
        }

        return true;
      }

      base::Result<base::Message> Type2Handler::CreateMessage(
          const base::Message& input_message) const {
        if (input_message.getTyp() != 2) {
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("Type2Handler: Creating Type 2 multi frame test message");

        // 创建输出消息对象，从输入消息复制参数
        base::Message message = input_message;

        // 设置Type 2的默认值（如果未设置）
        message.setTyp(2);

        if (message.getVsq() == 0x00) {
          message.setVsq(DEFAULT_VSQ);
        }
        if (message.getCot() == 0x00) {
          message.setCot(DEFAULT_COT);
        }
        if (message.getFun() == 0x00) {
          message.setFun(DEFAULT_FUN);
        }
        if (message.getInf() == 0x00) {
          message.setInf(DEFAULT_INF);
        }

        // 设置测试数据（如果没有提供）
        if (message.getTextContent().empty() && message.getVariableStructure().empty()) {
          message.setTextContent(DEFAULT_TEST_DATA);
        }

        spdlog::info("Type2Handler: Created multi frame test message to target {}",
                     message.getTarget());
        spdlog::debug(
            "Type2Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
            message.getVsq(), message.getCot(), message.getFun(), message.getInf());

        return message;
      }

      base::Result<MessageResult> Type2Handler::ProcessResponse(
          const base::Message& response) const {
        spdlog::debug("Type2Handler: Processing Type 2 response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();

        // 打印响应详情
        spdlog::info(
            "Type2Handler: Received response - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
            "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
            response.getTyp(), response.getVsq(), response.getCot(), response.getSource(),
            response.getTarget(), response.getFun(), response.getInf());

        // 提取响应内容
        std::string response_content = response.getTextContent();
        std::vector<uint8_t> response_data = response.getVariableStructure();

        MessageResult result;
        result.success = true;

        // 分析响应类型和传送原因
        if (response_type == 2) {
          // Type 2 响应
          if (response_cot == 0x07) {  // 激活确认
            result.description = "Multi frame test confirmed";
            spdlog::info("Type2Handler: Multi frame test confirmed by server");
          } else if (response_cot == 0x08) {  // 停止激活确认
            result.description = "Multi frame test stopped";
            spdlog::info("Type2Handler: Multi frame test stopped by server");
          } else {
            result.description
                = "Multi frame test response with COT " + std::to_string(response_cot);
            spdlog::info("Type2Handler: Multi frame test response with COT {:02X}", response_cot);
          }
        } else {
          // 其他类型的响应
          result.description = "Unexpected response type " + std::to_string(response_type);
          spdlog::warn("Type2Handler: Unexpected response type {} for Type 2 request",
                       response_type);
        }

        // 如果有文本内容，添加到结果中
        if (!response_content.empty()) {
          result.description += " - Content: " + response_content;
          spdlog::debug("Type2Handler: Response content: {}", response_content);
        }

        // 如果有可变结构体数据，保存到结果中
        if (!response_data.empty()) {
          result.data = response_data;
          spdlog::debug("Type2Handler: Response has {} bytes of variable data",
                        response_data.size());
        }

        return result;
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan
