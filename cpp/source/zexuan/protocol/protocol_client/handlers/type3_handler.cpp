/**
 * @file type3_handler.cpp
 * @brief Type 3 时间戳事件消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type3_handler.hpp"

#include <spdlog/spdlog.h>

namespace zexuan {
  namespace protocol {
    namespace client {

      Type3Handler::Type3Handler() {
        spdlog::debug("Type3Handler: Created for timestamp event messages");
      }

      bool Type3Handler::ValidateRequest(const base::Message& message) const {
        if (message.getTyp() != 3) {
          return false;
        }

        // 检查目标地址是否合理
        if (message.getTarget() == 0) {
          spdlog::debug("Type3Handler: Target address should not be 0");
          return false;
        }

        return true;
      }

      base::Result<base::Message> Type3Handler::CreateMessage(
          const base::Message& input_message) const {
        if (input_message.getTyp() != 3) {
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }

        spdlog::debug("Type3Handler: Creating Type 3 timestamp event message");

        // 创建输出消息对象，从输入消息复制参数
        base::Message message = input_message;

        // 设置Type 3的默认值（如果未设置）
        message.setTyp(3);

        if (message.getVsq() == 0x00) {
          message.setVsq(DEFAULT_VSQ);
        }
        if (message.getCot() == 0x00) {
          message.setCot(DEFAULT_COT);
        }
        if (message.getFun() == 0x00) {
          message.setFun(DEFAULT_FUN);
        }
        if (message.getInf() == 0x00) {
          message.setInf(DEFAULT_INF);
        }

        // 如果没有提供时间戳数据，生成当前时间戳
        if (message.getVariableStructure().empty()) {
          std::vector<uint8_t> timestamp_data(IEC103_TIMESTAMP_SIZE);
          auto now = std::chrono::system_clock::now();
          auto time_t = std::chrono::system_clock::to_time_t(now);
          auto tm = *std::localtime(&time_t);

          // 简化的时间戳格式（实际应该按IEC103格式）
          timestamp_data[0] = tm.tm_sec;
          timestamp_data[1] = tm.tm_min;
          timestamp_data[2] = tm.tm_hour;
          timestamp_data[3] = tm.tm_mday;
          timestamp_data[4] = tm.tm_mon + 1;
          timestamp_data[5] = (tm.tm_year + 1900) & 0xFF;
          timestamp_data[6] = ((tm.tm_year + 1900) >> 8) & 0xFF;

          message.setVariableStructure(timestamp_data);
        }

        spdlog::info("Type3Handler: Created timestamp event message to target {}",
                     message.getTarget());
        spdlog::debug(
            "Type3Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
            message.getVsq(), message.getCot(), message.getFun(), message.getInf());

        return message;
      }

      base::Result<MessageResult> Type3Handler::ProcessResponse(
          const base::Message& response) const {
        spdlog::debug("Type3Handler: Processing Type 3 response");

        uint8_t response_type = response.getTyp();
        if (response_type != 3) {
          spdlog::warn("Type3Handler: Unexpected response type: {}, expected: 3", response_type);
        }

        spdlog::debug(
            "Type3Handler: Response details - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
            "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
            response.getTyp(), response.getVsq(), response.getCot(), response.getSource(),
            response.getTarget(), response.getFun(), response.getInf());

        // 提取响应内容
        std::string response_content = response.getTextContent();
        std::vector<uint8_t> response_data = response.getVariableStructure();

        MessageResult result;
        result.success = true;
        result.description = "Type 3 timestamp event response processed successfully";

        // 如果有时间戳数据，解析并记录
        if (!response_data.empty() && response_data.size() >= IEC103_TIMESTAMP_SIZE) {
          result.description += " - Timestamp data received";
          result.data = response_data;
          spdlog::debug("Type3Handler: Response has {} bytes of timestamp data",
                        response_data.size());
        }

        // 如果有文本内容，记录到日志
        if (!response_content.empty()) {
          result.description += " - Content: " + response_content;
          spdlog::info("Type3Handler: Response text content: \"{}\"", response_content);
        }

        spdlog::info("Type3Handler: Successfully processed Type 3 response from source {}",
                     response.getSource());

        return result;
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan