/**
 * @file type4_handler.cpp
 * @brief Type 4 本地状态消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type4_handler.hpp"

#include <spdlog/spdlog.h>

#include <iomanip>
#include <sstream>

namespace zexuan {
  namespace protocol {
    namespace client {

      Type4Handler::Type4Handler() {
        spdlog::debug("Type4Handler: Created for local status messages");
      }

      bool Type4Handler::ValidateRequest(const base::Message& message) const {
        if (message.getTyp() != 4) {
          return false;
        }

        // 检查目标地址是否合理
        if (message.getTarget() == 0) {
          spdlog::debug("Type4Handler: Target address should not be 0");
          return false;
        }

        // VSQ应该是单信息元素类型
        if (message.getVsq() != DEFAULT_VSQ && message.getVsq() != 0x81) {
          spdlog::debug("Type4Handler: VSQ should be single element type, got {:02X}",
                        message.getVsq());
          // 这不是错误，只是警告
        }

        // 功能类型应该是本地状态查询相关
        if (message.getFun() != DEFAULT_FUN && message.getFun() != 0xFF
            && message.getFun() != 0x00) {
          spdlog::debug("Type4Handler: FUN should be status query type, got {:02X}",
                        message.getFun());
        }

        return true;
      }

      base::Result<base::Message> Type4Handler::CreateMessage(
          const base::Message& input_message) const {
        if (input_message.getTyp() != 4) {
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }
        spdlog::debug("Type4Handler: Creating Type 4 local status request message");

        // 创建输出消息对象，从输入消息复制参数
        base::Message message = input_message;

        // 设置Type 4的默认值（如果未设置）
        message.setTyp(4);

        if (message.getVsq() == 0x00) {
          message.setVsq(DEFAULT_VSQ);
        }
        if (message.getCot() == 0x00) {
          message.setCot(DEFAULT_COT);
        }
        if (message.getFun() == 0x00) {
          message.setFun(DEFAULT_FUN);
        }
        if (message.getInf() == 0x00) {
          message.setInf(DEFAULT_INF);
        }

        spdlog::info("Type4Handler: Created local status request message to target {}",
                     message.getTarget());
        spdlog::debug(
            "Type4Handler: Message details - VSQ={:02X}, COT={:02X}, FUN={:02X}, INF={:02X}",
            message.getVsq(), message.getCot(), message.getFun(), message.getInf());

        return message;
      }

      base::Result<MessageResult> Type4Handler::ProcessResponse(
          const base::Message& response) const {
        spdlog::debug("Type4Handler: Processing Type 4 local status response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();

        // 打印响应详情
        spdlog::info(
            "Type4Handler: Received local status response - TYP={:02X}, VSQ={:02X}, COT={:02X}, "
            "SRC={:02X}, TGT={:02X}, FUN={:02X}, INF={:02X}",
            response.getTyp(), response.getVsq(), response.getCot(), response.getSource(),
            response.getTarget(), response.getFun(), response.getInf());

        // 提取状态数据
        std::vector<uint8_t> status_data = response.getVariableStructure();

        MessageResult result;
        result.data = status_data;

        if (status_data.size() >= MIN_STATUS_DATA_SIZE) {
          // 解析状态响应
          result.description = ParseStatusResponse(status_data);
          result.success = true;
          spdlog::info("Type4Handler: {}", result.description);
        } else {
          result.description = "Invalid local status response (insufficient data)";
          result.success = false;
          spdlog::error("Type4Handler: Invalid status data (size: {}, expected >= {})",
                        status_data.size(), MIN_STATUS_DATA_SIZE);
        }

        return result;
      }

      // 私有方法实现
      std::vector<uint8_t> Type4Handler::CreateDefaultRequestData() const {
        std::vector<uint8_t> request_data;
        request_data.push_back(0x01);  // 请求类型：状态查询
        request_data.push_back(0x02);  // 请求参数：包含时间戳
        return request_data;
      }

      std::string Type4Handler::ParseStatusResponse(const std::vector<uint8_t>& status_data) const {
        std::ostringstream oss;

        // 解析时间戳（前7字节）
        if (status_data.size() >= IEC103_TIMESTAMP_SIZE) {
          std::vector<uint8_t> timestamp_data(status_data.begin(),
                                              status_data.begin() + IEC103_TIMESTAMP_SIZE);
          std::string time_str = FormatTimestamp(timestamp_data);
          oss << "Response Time: " << time_str;
        }

        // 解析状态数据（从第8字节开始）
        if (status_data.size() >= MIN_STATUS_DATA_SIZE) {
          uint8_t device_status = status_data[7];
          uint8_t comm_status = status_data[8];
          uint8_t run_mode = status_data[9];
          uint8_t alarm_status = status_data[10];

          oss << ", Device: " << ParseDeviceStatus(device_status);
          oss << ", Comm: " << ParseCommStatus(comm_status);
          oss << ", Mode: " << ParseRunMode(run_mode);
          oss << ", Alarm: " << ParseAlarmStatus(alarm_status);

          // 解析负载状态（4字节，如果有的话）
          if (status_data.size() >= FULL_STATUS_DATA_SIZE) {
            uint32_t load_percent = status_data[11] | (static_cast<uint32_t>(status_data[12]) << 8)
                                    | (static_cast<uint32_t>(status_data[13]) << 16)
                                    | (static_cast<uint32_t>(status_data[14]) << 24);
            oss << ", Load: " << load_percent << "%";
          }
        }

        return oss.str();
      }

      std::string Type4Handler::ParseDeviceStatus(uint8_t status_byte) const {
        switch (status_byte) {
          case 0x01:
            return "Online";
          case 0x00:
            return "Offline";
          default:
            return "Unknown(" + std::to_string(status_byte) + ")";
        }
      }

      std::string Type4Handler::ParseCommStatus(uint8_t status_byte) const {
        switch (status_byte) {
          case 0x02:
            return "Normal";
          case 0x01:
            return "Error";
          case 0x00:
            return "Disconnected";
          default:
            return "Unknown(" + std::to_string(status_byte) + ")";
        }
      }

      std::string Type4Handler::ParseRunMode(uint8_t mode_byte) const {
        switch (mode_byte) {
          case 0x03:
            return "Auto";
          case 0x02:
            return "Manual";
          case 0x01:
            return "Test";
          case 0x00:
            return "Stopped";
          default:
            return "Unknown(" + std::to_string(mode_byte) + ")";
        }
      }

      std::string Type4Handler::ParseAlarmStatus(uint8_t alarm_byte) const {
        switch (alarm_byte) {
          case 0x00:
            return "No Alarm";
          case 0x01:
            return "Minor Alarm";
          case 0x02:
            return "Major Alarm";
          case 0x03:
            return "Critical Alarm";
          default:
            return "Unknown(" + std::to_string(alarm_byte) + ")";
        }
      }

      std::string Type4Handler::FormatTimestamp(const std::vector<uint8_t>& timestamp_data) const {
        if (timestamp_data.size() < IEC103_TIMESTAMP_SIZE) {
          return "Invalid timestamp";
        }

        // 解析IEC 60870-5-103时间格式（7字节）
        uint16_t milliseconds = timestamp_data[0] | (static_cast<uint16_t>(timestamp_data[1]) << 8);
        uint8_t minute = timestamp_data[2];
        uint8_t hour = timestamp_data[3];
        uint8_t day = timestamp_data[4];
        uint8_t month = timestamp_data[5];
        uint8_t year = timestamp_data[6];

        std::ostringstream oss;
        oss << "20" << std::setfill('0') << std::setw(2) << static_cast<int>(year) << "-"
            << std::setfill('0') << std::setw(2) << static_cast<int>(month) << "-"
            << std::setfill('0') << std::setw(2) << static_cast<int>(day) << " "
            << std::setfill('0') << std::setw(2) << static_cast<int>(hour) << ":"
            << std::setfill('0') << std::setw(2) << static_cast<int>(minute) << "."
            << std::setfill('0') << std::setw(3) << static_cast<int>(milliseconds);

        return oss.str();
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan
