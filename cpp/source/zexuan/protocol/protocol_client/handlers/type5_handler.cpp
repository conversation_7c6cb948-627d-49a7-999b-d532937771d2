/**
 * @file type5_handler.cpp
 * @brief Type 5 重命名请求消息处理器实现
 * <AUTHOR>
 * @date 2024
 */

#include "zexuan/protocol/protocol_client/handlers/type5_handler.hpp"

#include <spdlog/spdlog.h>

namespace zexuan {
  namespace protocol {
    namespace client {

      // 静态常量定义
      const std::string Type5Handler::DEFAULT_RENAME_PATH = "./files/";

      Type5Handler::Type5Handler() {
        spdlog::debug("Type5Handler: Created for rename request messages");
      }

      bool Type5Handler::ValidateRequest(const base::Message& message) const {
        if (message.getTyp() != 5) {
          return false;
        }
        // 检查目标地址是否合理
        if (message.getTarget() == 0) {
          spdlog::debug("Type5Handler: Target address should not be 0");
          return false;
        }

        // 检查是否有重命名路径
        if (message.getTextContent().empty()) {
          spdlog::debug("Type5Handler: No rename path provided, will use default");
        }

        return true;
      }

      base::Result<base::Message> Type5Handler::CreateMessage(
          const base::Message& input_message) const {
        if (input_message.getTyp() != 5) {
          return std::unexpected(base::ErrorCode::INVALID_PARAMETER);
        }
        spdlog::debug("Type5Handler: Creating Type 5 rename request message");

        // 创建输出消息对象，从输入消息复制参数
        base::Message message = input_message;

        // 设置Type 5的默认值（如果未设置）
        message.setTyp(5);

        if (message.getVsq() == 0x00) {
          message.setVsq(DEFAULT_VSQ);
        }
        if (message.getCot() == 0x00) {
          message.setCot(DEFAULT_COT);
        }
        if (message.getFun() == 0x00) {
          message.setFun(DEFAULT_FUN);
        }
        if (message.getInf() == 0x00) {
          message.setInf(DEFAULT_INF);
        }

        // 设置重命名路径（如果没有提供）
        if (message.getTextContent().empty()) {
          message.setTextContent(DEFAULT_RENAME_PATH);
          spdlog::debug("Type5Handler: Using default rename path: {}", DEFAULT_RENAME_PATH);
        }

        spdlog::info("Type5Handler: Created rename request message to target {} for path: {}",
                     message.getTarget(), message.getTextContent());

        return message;
      }

      base::Result<MessageResult> Type5Handler::ProcessResponse(
          const base::Message& response) const {
        spdlog::debug("Type5Handler: Processing Type 5 rename response");

        uint8_t response_type = response.getTyp();
        uint8_t response_cot = response.getCot();

        // 提取响应内容
        std::string response_content = response.getTextContent();
        std::vector<uint8_t> response_data = response.getVariableStructure();

        MessageResult result;
        result.data = response_data;

        // 分析响应
        if (response_type == 5) {
          result.success = true;
          result.description = ParseRenameResult(response_cot, response_content);
        } else {
          result.success = false;
          result.description
              = "Unexpected response type " + std::to_string(response_type) + " for rename request";
        }

        return result;
      }

      // 私有方法实现
      bool Type5Handler::ValidatePath(const std::string& path) const {
        if (path.empty()) {
          return false;
        }

        // 检查路径长度
        if (path.length() > 255) {
          return false;
        }

        return true;
      }

      std::string Type5Handler::ParseRenameResult(uint8_t response_cot,
                                                  const std::string& content) const {
        std::string result;

        switch (response_cot) {
          case 0x07:  // 激活确认
            result = "Rename request confirmed";
            break;
          case 0x08:  // 停止激活确认
            result = "Rename request stopped";
            break;
          case 0x2D:  // 命令传送的否定确认
            result = "Rename request rejected";
            break;
          default:
            result = "Rename response with COT " + std::to_string(response_cot);
            break;
        }

        return result;
      }

    }  // namespace client
  }  // namespace protocol
}  // namespace zexuan
