#include "zexuan/protocol/protocol_server/server/protocol_server.hpp"

#include <spdlog/spdlog.h>

#include <fstream>

#include "zexuan/protocol/protocol_server/gateway/protocol_gateway.hpp"

namespace zexuan {
  namespace protocol {
    namespace server {

      ProtocolServer::ProtocolServer(event::EventLoop* event_loop,
                                     const std::string& config_file_path)
          : LifecycleComponentBase("ProtocolServer"),
            event_loop_(event_loop),
            config_file_path_(config_file_path) {
        spdlog::info("{}: Component created, ready to start", GetComponentName());
      }

      ProtocolServer::~ProtocolServer() {
        // 确保在析构前调用Stop()
        if (GetState() != base::ComponentState::STOPPED) {
          spdlog::warn("{}: Component not stopped before destruction, forcing stop",
                       GetComponentName());
          Stop();
        }
        spdlog::info("{}: Component destroyed", GetComponentName());
      }

      bool ProtocolServer::Start() {
        if (!TransitionTo(base::ComponentState::STARTING)) {
          return false;
        }

        try {
          spdlog::info("{}: Starting component", GetComponentName());

          // 1. 读取配置文件
          spdlog::debug("{}: Loading configuration from {}", GetComponentName(), config_file_path_);
          if (!LoadConfiguration()) {
            spdlog::error("{}: Failed to load configuration", GetComponentName());
            TransitionTo(base::ComponentState::ERROR);
            return false;
          }
          spdlog::info("{}: Configuration loaded successfully", GetComponentName());

          // 2. 创建TcpServer（使用外部传入的EventLoop）
          spdlog::debug("{}: Creating TcpServer on {}:{}", GetComponentName(), listen_address_,
                        listen_port_);
          zexuan::event::Address listen_addr(listen_address_, listen_port_);
          tcp_server_
              = std::make_unique<event::TcpServer>(event_loop_, listen_addr, "ProtocolServer");

          // 3. 设置回调
          spdlog::debug("{}: Setting up network callbacks", GetComponentName());
          tcp_server_->setConnectionCallback(
              [this](const event::TcpConnectionPtr& conn) { OnConnection(conn); });

          tcp_server_->setMessageCallback(
              [this](const event::TcpConnectionPtr& conn, event::Buffer* buffer,
                     event::Timestamp receiveTime) { OnMessage(conn, buffer, receiveTime); });

          tcp_server_->setWriteCompleteCallback(
              [this](const event::TcpConnectionPtr& conn) { OnWriteComplete(conn); });

          // 4. 设置线程池大小
          spdlog::debug("{}: Setting thread pool size to {}", GetComponentName(),
                        thread_pool_size_);
          tcp_server_->setThreadNum(thread_pool_size_);

          // 5. 创建Mediator
          spdlog::debug("{}: Creating Mediator", GetComponentName());
          mediator_ = std::make_shared<base::Mediator>();

          // 6. 启动TcpServer
          spdlog::debug("{}: Starting TcpServer", GetComponentName());
          tcp_server_->start();

          if (!TransitionTo(base::ComponentState::RUNNING)) {
            return false;
          }

          spdlog::info("{}: Component started successfully on {}:{}", GetComponentName(),
                       listen_address_, listen_port_);
          return true;

        } catch (const std::exception& e) {
          spdlog::error("{}: Failed to start component: {}", GetComponentName(), e.what());
          TransitionTo(base::ComponentState::ERROR);
          return false;
        }
      }

      bool ProtocolServer::Stop() {
        if (GetState() == base::ComponentState::STOPPED) {
          return true;  // 已经停止
        }

        if (!TransitionTo(base::ComponentState::STOPPING)) {
          return false;
        }

        spdlog::info("{}: Stopping component", GetComponentName());

        try {
          // 1. 停止接受新连接
          spdlog::debug("{}: Stopping TcpServer", GetComponentName());
          if (tcp_server_) {
            tcp_server_.reset();  // 这会停止接受新连接并清理现有连接
          }

          // 2. 清理所有连接和Gateway
          spdlog::debug("{}: Cleaning up connections and gateways", GetComponentName());
          {
            std::lock_guard<std::mutex> lock(connections_mutex_);
            for (const auto& conn : connections_) {
              if (conn && conn->connected()) {
                // 确保 forceClose 在连接所属的 EventLoop 线程中执行
                auto* connLoop = conn->getLoop();
                if (connLoop) {
                  connLoop->runInLoop([conn]() {
                    if (conn->connected()) {
                      conn->forceClose();
                    }
                  });
                }
              }
              DestroyGatewayForConnection(conn);
            }
            connections_.clear();
          }

          // 3. 清理Gateway映射
          {
            std::lock_guard<std::mutex> lock(gateways_mutex_);
            gateways_.clear();
          }

          // 4. 清理Service ID映射
          {
            std::lock_guard<std::mutex> lock(service_id_mutex_);
            connection_service_ids_.clear();
            allocated_service_ids_.clear();
            next_service_id_ = base::DYNAMIC_SERVICE_ID_START;
          }

          is_running_.store(false);
          is_initialized_.store(false);

          if (!TransitionTo(base::ComponentState::STOPPED)) {
            return false;
          }

          spdlog::info("{}: Component stopped successfully", GetComponentName());
          return true;

        } catch (const std::exception& e) {
          spdlog::error("{}: Failed to stop component: {}", GetComponentName(), e.what());
          TransitionTo(base::ComponentState::ERROR);
          return false;
        }
      }

      bool ProtocolServer::SendData(const event::TcpConnectionPtr& conn,
                                    const std::vector<uint8_t>& data) {
        if (conn && conn->connected()) {
          conn->send(data.data(), data.size());
          stats_.bytes_sent += data.size();
          spdlog::debug("Sent {} bytes to connection {}", data.size(), conn->name());
          return true;
        }

        spdlog::error("Failed to send data: connection not found or disconnected");
        return false;
      }

      // 网络回调处理
      void ProtocolServer::OnConnection(const event::TcpConnectionPtr& conn) {
        if (conn->connected()) {
          // 新连接建立
          AddConnection(conn);

          // 为连接创建Gateway
          if (CreateGatewayForConnection(conn)) {
            stats_.total_connections++;
            stats_.active_connections++;
            spdlog::info("New connection established: {}", conn->name());
          } else {
            spdlog::error("Failed to create Gateway for connection: {}", conn->name());
            conn->forceClose();
          }
        } else {
          // 连接断开
          RemoveConnection(conn);
          spdlog::info("Connection closed: {}", conn->name());
        }
      }

      void ProtocolServer::OnMessage(const event::TcpConnectionPtr& conn, event::Buffer* buffer,
                                     event::Timestamp receiveTime) {
        // 统计接收字节数
        stats_.bytes_received += buffer->readableBytes();

        // 在一次回调中处理所有完整帧（直接使用TcpConnection）
        while (ExtractOneFrame(buffer, conn)) {
          // 继续提取下一帧
        }
      }

      void ProtocolServer::OnWriteComplete(const event::TcpConnectionPtr& conn) {
        spdlog::debug("Write completed for connection: {}", conn->name());
      }

      bool ProtocolServer::ExtractOneFrame(event::Buffer* buffer,
                                           const event::TcpConnectionPtr& conn) {
        // 检查最小帧长度（0x68 + LEN_L + LEN_H）
        if (buffer->readableBytes() < 3) {
          return false;
        }

        // 查找帧起始符 0x68
        const char* start
            = static_cast<const char*>(memchr(buffer->peek(), 0x68, buffer->readableBytes()));

        if (!start) {
          // 没有找到起始符，丢弃所有数据
          size_t discarded = buffer->readableBytes();
          buffer->retrieveAll();
          spdlog::warn("Connection {}: No frame start found, discarding {} bytes", conn->name(),
                       discarded);
          return false;
        }

        // 跳过起始符之前的垃圾数据
        size_t skip = start - buffer->peek();
        if (skip > 0) {
          buffer->retrieve(skip);
          spdlog::warn("Connection {}: Skipped {} bytes of garbage data", conn->name(), skip);
        }

        // 再次检查是否有足够的数据读取帧头
        if (buffer->readableBytes() < 3) {
          return false;
        }

        // 解析帧长度：LEN_L + LEN_H
        uint8_t len_l = static_cast<uint8_t>(buffer->peek()[1]);
        uint8_t len_h = static_cast<uint8_t>(buffer->peek()[2]);
        uint16_t asdu_len = len_l | (static_cast<uint16_t>(len_h) << 8);
        size_t total_frame_len = 3 + asdu_len;  // START + LEN_L + LEN_H + ASDU

        // 帧长度合法性检查（IEC 60870-5-103 最大帧长度约1024字节）
        if (asdu_len > 1024) {
          buffer->retrieve(1);  // 跳过这个错误的起始符
          spdlog::warn("Connection {}: Invalid frame length {}, skipping", conn->name(), asdu_len);
          return true;  // 继续尝试下一个字节
        }

        // 检查是否有完整帧
        if (buffer->readableBytes() < total_frame_len) {
          // 不完整帧：保留在 buffer 中，等待下次 onMessage
          spdlog::debug("Connection {}: Incomplete frame, need {} bytes, have {} bytes",
                        conn->name(), total_frame_len, buffer->readableBytes());
          return false;
        }

        // 提取完整帧
        std::vector<uint8_t> frame_data(total_frame_len);
        memcpy(frame_data.data(), buffer->peek(), total_frame_len);
        buffer->retrieve(total_frame_len);

        // 发送给 Gateway
        SendFrameToGateway(conn, frame_data);

        spdlog::debug("Connection {}: Extracted complete frame of {} bytes", conn->name(),
                      total_frame_len);
        return true;  // 继续处理下一帧
      }

      void ProtocolServer::SendFrameToGateway(const event::TcpConnectionPtr& conn,
                                              const std::vector<uint8_t>& frame_data) {
        std::lock_guard<std::mutex> lock(gateways_mutex_);
        auto gateway_it = gateways_.find(conn);
        if (gateway_it != gateways_.end()) {
          // 发送完整帧给Gateway处理（每个Gateway对应一个连接）
          gateway_it->second->OnNetworkProtocolData(frame_data);
          spdlog::debug("Sent complete frame to Gateway for connection {}: {} bytes", conn->name(),
                        frame_data.size());
        } else {
          spdlog::error("No Gateway found for connection {}", conn->name());
        }
      }

      // 连接管理方法（简化：直接管理TcpConnection）
      void ProtocolServer::AddConnection(const event::TcpConnectionPtr& conn) {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        connections_.insert(conn);
      }

      void ProtocolServer::RemoveConnection(const event::TcpConnectionPtr& conn) {
        std::lock_guard<std::mutex> lock(connections_mutex_);

        // 销毁对应的Gateway
        DestroyGatewayForConnection(conn);

        // 移除连接
        connections_.erase(conn);

        stats_.active_connections--;
        spdlog::info("Connection removed: {}", conn->name());
      }

      // Gateway管理方法（直接使用TcpConnection）
      bool ProtocolServer::CreateGatewayForConnection(const event::TcpConnectionPtr& conn) {
        try {
          // 为连接分配唯一的Service ID
          base::ObjectId service_id = AllocateServiceId(conn);
          if (service_id == base::INVALID_ID) {
            spdlog::error("Failed to allocate Service ID for connection {}", conn->name());
            return false;
          }

          // 创建 Mediator（每个Gateway需要独立的Mediator）
          auto mediator = std::make_shared<base::Mediator>();

          // 直接创建 Gateway，传递配置文件路径和 Mediator（使用重构后的构造函数）
          auto gateway = std::make_shared<protocol::gateway::ProtocolGateway>(config_file_path_,
                                                                              mediator, service_id);

          // 设置Gateway的发送回调，让它能够通过TCPServer发送数据（每个Gateway对应一个连接）
          gateway->SetSendCallback([this, conn](const std::vector<uint8_t>& data) -> bool {
            return SendData(conn, data);
          });

          // 启动Gateway
          if (!gateway->Start()) {
            spdlog::error("Failed to start Gateway for connection {}", conn->name());
            return false;
          }

          // 存储Gateway
          {
            std::lock_guard<std::mutex> lock(gateways_mutex_);
            gateways_[conn] = gateway;
          }

          spdlog::info("Created Gateway for connection {}", conn->name());
          return true;

        } catch (const std::exception& e) {
          spdlog::error("Exception creating Gateway for connection {}: {}", conn->name(), e.what());
          return false;
        }
      }

      void ProtocolServer::DestroyGatewayForConnection(const event::TcpConnectionPtr& conn) {
        std::lock_guard<std::mutex> lock(gateways_mutex_);
        auto it = gateways_.find(conn);
        if (it != gateways_.end()) {
          // 停止Gateway
          it->second->Stop();

          // 移除Gateway
          gateways_.erase(it);

          spdlog::info("Destroyed Gateway for connection {}", conn->name());
        }

        // 释放Service ID
        ReleaseServiceId(conn);
      }

      base::ObjectId ProtocolServer::AllocateServiceId(const event::TcpConnectionPtr& conn) {
        std::lock_guard<std::mutex> lock(service_id_mutex_);

        // 检查连接是否已经分配了ID
        auto existing_it = connection_service_ids_.find(conn);
        if (existing_it != connection_service_ids_.end()) {
          spdlog::warn("Connection {} already has Service ID: {}", conn->name(),
                       existing_it->second);
          return existing_it->second;
        }

        // 寻找下一个可用的ID
        base::ObjectId candidate_id = next_service_id_;
        while (candidate_id <= base::DYNAMIC_SERVICE_ID_END) {
          if (allocated_service_ids_.find(candidate_id) == allocated_service_ids_.end()) {
            // 找到可用ID
            allocated_service_ids_.insert(candidate_id);
            connection_service_ids_[conn] = candidate_id;
            next_service_id_ = candidate_id + 1;

            spdlog::info("Allocated Service ID {} for connection {}", candidate_id, conn->name());
            return candidate_id;
          }
          candidate_id++;
        }

        // 如果到达范围末尾，从头开始寻找空闲ID
        candidate_id = base::DYNAMIC_SERVICE_ID_START;
        while (candidate_id < next_service_id_) {
          if (allocated_service_ids_.find(candidate_id) == allocated_service_ids_.end()) {
            allocated_service_ids_.insert(candidate_id);
            connection_service_ids_[conn] = candidate_id;

            spdlog::info("Allocated Service ID {} for connection {} (recycled)", candidate_id,
                         conn->name());
            return candidate_id;
          }
          candidate_id++;
        }

        spdlog::error("No available Service IDs for connection {}", conn->name());
        return base::INVALID_ID;
      }

      void ProtocolServer::ReleaseServiceId(const event::TcpConnectionPtr& conn) {
        std::lock_guard<std::mutex> lock(service_id_mutex_);

        auto it = connection_service_ids_.find(conn);
        if (it != connection_service_ids_.end()) {
          base::ObjectId service_id = it->second;

          // 从分配集合中移除
          allocated_service_ids_.erase(service_id);
          connection_service_ids_.erase(it);

          spdlog::info("Released Service ID {} for connection {}", service_id, conn->name());
        }
      }

      // 状态查询方法
      size_t ProtocolServer::GetConnectionCount() const {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        return connections_.size();
      }

      std::vector<std::string> ProtocolServer::GetAllConnectionNames() const {
        std::lock_guard<std::mutex> lock(connections_mutex_);

        std::vector<std::string> names;
        names.reserve(connections_.size());

        for (const auto& conn : connections_) {
          names.push_back(conn->name());
        }

        return names;
      }

      void ProtocolServer::ResetStatistics() {
        stats_.total_connections.store(0);
        stats_.active_connections.store(0);
        stats_.bytes_sent.store(0);
        stats_.bytes_received.store(0);
      }

      bool ProtocolServer::LoadConfiguration() {
        std::ifstream config_file(config_file_path_);
        if (!config_file.is_open()) {
          spdlog::error("{}: Cannot open config file: {}", GetComponentName(), config_file_path_);
          return false;
        }

        nlohmann::json config;
        try {
          config_file >> config;
        } catch (const std::exception& e) {
          spdlog::error("{}: Failed to parse config file {}: {}", GetComponentName(),
                        config_file_path_, e.what());
          return false;
        }

        // 读取服务器配置
        if (!config.contains("protocol") || !config["protocol"].contains("server")) {
          spdlog::error("{}: Missing protocol.server section in config file: {}",
                        GetComponentName(), config_file_path_);
          return false;
        }

        auto server_config = config["protocol"]["server"];

        // 读取必需的配置项，不允许默认值
        if (!server_config.contains("listen_address")) {
          spdlog::error("{}: Missing required config: protocol.server.listen_address",
                        GetComponentName());
          return false;
        }
        if (!server_config.contains("listen_port")) {
          spdlog::error("{}: Missing required config: protocol.server.listen_port",
                        GetComponentName());
          return false;
        }
        if (!server_config.contains("max_connections")) {
          spdlog::error("{}: Missing required config: protocol.server.max_connections",
                        GetComponentName());
          return false;
        }
        if (!server_config.contains("thread_pool_size")) {
          spdlog::error("{}: Missing required config: protocol.server.thread_pool_size",
                        GetComponentName());
          return false;
        }

        listen_address_ = server_config["listen_address"];
        listen_port_ = server_config["listen_port"];
        max_connections_ = server_config["max_connections"];
        thread_pool_size_ = server_config["thread_pool_size"];

        spdlog::debug("{}: Loaded configuration - address: {}:{}, max_conn: {}, threads: {}",
                      GetComponentName(), listen_address_, listen_port_, max_connections_,
                      thread_pool_size_);
        return true;
      }

    }  // namespace server
  }  // namespace protocol
}  // namespace zexuan