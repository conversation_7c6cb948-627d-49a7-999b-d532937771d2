/**
 * @file message_protocol.cpp
 * @brief TCP消息协议处理实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/utils/message_protocol.hpp"

#include <arpa/inet.h>
#include <spdlog/spdlog.h>

#include <cstring>

#include "nlohmann/json.hpp"

namespace zexuan {
  namespace utils {

    std::vector<uint8_t> MessageProtocol::packMessage(const std::string& json_content) {
      if (json_content.empty()) {
        spdlog::error("Cannot pack empty JSON content");
        return {};
      }

      if (json_content.size() > MAX_MESSAGE_SIZE) {
        spdlog::error("JSON content too large: {} bytes (max: {})", json_content.size(),
                      MAX_MESSAGE_SIZE);
        return {};
      }

      if (!isValidJson(json_content)) {
        spdlog::error("Invalid JSON content");
        return {};
      }

      std::vector<uint8_t> packed_message;
      packed_message.reserve(HEADER_SIZE + json_content.size());

      // 打包消息长度（网络字节序）
      uint32_t content_length = static_cast<uint32_t>(json_content.size());
      uint32_t network_length = hostToNetwork(content_length);

      const uint8_t* length_bytes = reinterpret_cast<const uint8_t*>(&network_length);
      packed_message.insert(packed_message.end(), length_bytes, length_bytes + HEADER_SIZE);

      // 打包JSON内容
      const uint8_t* content_bytes = reinterpret_cast<const uint8_t*>(json_content.data());
      packed_message.insert(packed_message.end(), content_bytes,
                            content_bytes + json_content.size());

      return packed_message;
    }

    std::optional<uint32_t> MessageProtocol::unpackHeader(const std::vector<uint8_t>& header_data) {
      if (header_data.size() != HEADER_SIZE) {
        spdlog::error("Invalid header size: {} (expected: {})", header_data.size(), HEADER_SIZE);
        return std::nullopt;
      }

      uint32_t network_length;
      std::memcpy(&network_length, header_data.data(), HEADER_SIZE);
      uint32_t content_length = networkToHost(network_length);

      if (content_length == 0) {
        spdlog::error("Invalid content length: 0");
        return std::nullopt;
      }

      if (content_length > MAX_MESSAGE_SIZE) {
        spdlog::error("Content length too large: {} (max: {})", content_length, MAX_MESSAGE_SIZE);
        return std::nullopt;
      }

      return content_length;
    }

    std::optional<std::string> MessageProtocol::unpackBody(const std::vector<uint8_t>& body_data,
                                                           uint32_t expected_length) {
      if (body_data.size() != expected_length) {
        spdlog::error("Body size mismatch: {} (expected: {})", body_data.size(), expected_length);
        return std::nullopt;
      }

      std::string json_content(reinterpret_cast<const char*>(body_data.data()), body_data.size());

      if (!isValidJson(json_content)) {
        spdlog::error("Invalid JSON content in message body");
        return std::nullopt;
      }

      return json_content;
    }

    bool MessageProtocol::validateMessage(const std::vector<uint8_t>& packed_message) {
      if (packed_message.size() < HEADER_SIZE) {
        return false;
      }

      // 提取并验证消息头
      std::vector<uint8_t> header(packed_message.begin(), packed_message.begin() + HEADER_SIZE);
      auto content_length_opt = unpackHeader(header);
      if (!content_length_opt) {
        return false;
      }

      uint32_t content_length = content_length_opt.value();

      // 验证总长度
      if (packed_message.size() != HEADER_SIZE + content_length) {
        return false;
      }

      // 验证消息体
      std::vector<uint8_t> body(packed_message.begin() + HEADER_SIZE, packed_message.end());
      auto json_content_opt = unpackBody(body, content_length);

      return json_content_opt.has_value();
    }

    std::optional<std::string> MessageProtocol::extractJsonFromPackedMessage(
        const std::vector<uint8_t>& packed_message) {
      if (!validateMessage(packed_message)) {
        return std::nullopt;
      }

      // 提取消息头
      std::vector<uint8_t> header(packed_message.begin(), packed_message.begin() + HEADER_SIZE);
      auto content_length_opt = unpackHeader(header);
      if (!content_length_opt) {
        return std::nullopt;
      }

      uint32_t content_length = content_length_opt.value();

      // 提取消息体
      std::vector<uint8_t> body(packed_message.begin() + HEADER_SIZE, packed_message.end());
      return unpackBody(body, content_length);
    }

    uint32_t MessageProtocol::hostToNetwork(uint32_t host_value) { return htonl(host_value); }

    uint32_t MessageProtocol::networkToHost(uint32_t network_value) { return ntohl(network_value); }

    bool MessageProtocol::isValidJson(const std::string& json_str) {
      try {
        nlohmann::json::parse(json_str);
        return true;
      } catch (const std::exception&) {
        return false;
      }
    }

    // MessageBuffer implementation
    void MessageBuffer::appendData(const std::vector<uint8_t>& data) {
      buffer_.insert(buffer_.end(), data.begin(), data.end());
    }

    void MessageBuffer::appendData(const uint8_t* data, size_t size) {
      buffer_.insert(buffer_.end(), data, data + size);
    }

    std::optional<std::string> MessageBuffer::extractMessage() {
      if (!hasCompleteMessage()) {
        return std::nullopt;
      }

      // 提取消息头
      std::vector<uint8_t> header(buffer_.begin(), buffer_.begin() + MessageProtocol::HEADER_SIZE);
      auto content_length_opt = MessageProtocol::unpackHeader(header);
      if (!content_length_opt) {
        // 头部无效，清空缓冲区
        clear();
        return std::nullopt;
      }

      uint32_t content_length = content_length_opt.value();
      size_t total_message_size = MessageProtocol::HEADER_SIZE + content_length;

      // 提取完整消息
      std::vector<uint8_t> complete_message(buffer_.begin(), buffer_.begin() + total_message_size);

      // 从缓冲区中移除已提取的消息
      removeData(total_message_size);

      // 提取JSON内容
      return MessageProtocol::extractJsonFromPackedMessage(complete_message);
    }

    bool MessageBuffer::hasCompleteMessage() const {
      if (buffer_.size() < MessageProtocol::HEADER_SIZE) {
        return false;
      }

      // 提取消息头
      std::vector<uint8_t> header(buffer_.begin(), buffer_.begin() + MessageProtocol::HEADER_SIZE);
      auto content_length_opt = MessageProtocol::unpackHeader(header);
      if (!content_length_opt) {
        return false;
      }

      uint32_t content_length = content_length_opt.value();
      size_t total_message_size = MessageProtocol::HEADER_SIZE + content_length;

      return buffer_.size() >= total_message_size;
    }

    void MessageBuffer::removeData(size_t length) {
      if (length >= buffer_.size()) {
        buffer_.clear();
      } else {
        buffer_.erase(buffer_.begin(), buffer_.begin() + length);
      }
    }

  }  // namespace utils
}  // namespace zexuan
