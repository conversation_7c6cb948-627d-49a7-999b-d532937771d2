use std::collections::HashMap;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{mpsc, RwLock};
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let listener = TcpListener::bind("127.0.0.1:34142").await?;
    println!("Chat server listening on 127.0.0.1:34142");

    let clients = Arc::new(RwLock::new(HashMap::new()));
    let mut client_id = 0;

    loop {
        let (socket, addr) = listener.accept().await?;
        println!("New connection from {}", addr);

        let clients = clients.clone();
        let (tx, mut rx) = mpsc::channel::<String>(100);

        let current_client_id = client_id;
        clients.write().await.insert(current_client_id, tx);
        client_id += 1;

        tokio::spawn(async move {
            let mut buffer = vec![0; 1024];
            let mut socket = socket;

            loop {
                tokio::select! {
                    result = socket.read(&mut buffer) => {
                        match result {
                            Ok(0) => {
                                println!("Client {} disconnected", current_client_id);
                                break;
                            }
                            Ok(n) => {
                                let message = String::from_utf8_lossy(&buffer[..n]).to_string();
                                println!("Received from client {}: {}", current_client_id, message);
                                
                                let broadcast_message = format!("Client {}: {}", current_client_id, message);
                                
                                let clients_lock = clients.read().await;
                                for (&id, client_tx) in clients_lock.iter() {
                                    if id != current_client_id {
                                        if let Err(e) = client_tx.send(broadcast_message.clone()).await {
                                            eprintln!("Failed to send message to client {}: {}", id, e);
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                eprintln!("Error reading from socket: {}", e);
                                break;
                            }
                        }
                    }
                    Some(message) = rx.recv() => {
                        if let Err(e) = socket.write_all(message.as_bytes()).await {
                            eprintln!("Error writing to socket: {}", e);
                            break;
                        }
                    }
                }
            }

            clients.write().await.remove(&current_client_id);
        });
    }
}
