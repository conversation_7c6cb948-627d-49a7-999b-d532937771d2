#!/bin/bash

# 定义 home 变量
home=$(pwd)

# 定义命令到包名的映射关系（pacman）
declare -A PACMAN_PKGS=(
    ["unzip"]="unzip"
    ["vim"]="vim"
    ["git"]="git"
    ["curl"]="curl"
    ["wget"]="wget"
    ["cmake"]="cmake"
    ["make"]="make"
    ["gcc"]="gcc"
    ["g++"]="g++"
    ["rust"]="rust"
    ["pnpm"]="pnpm"
    ["npm"]="npm"
    # 在这里添加更多映射
)

# 定义需要用 paru 安装的包（命令名到包名的映射）
declare -A PARU_PKGS=(
    ["mariadb"]="mariadb"
    ["conan"]="conan"
    # 在这里添加更多需要用 paru 安装的包
)


# 配置 pacman 镜像源
configure_mirror() {
    local mirror_file="/etc/pacman.d/mirrorlist"
    local ustc_mirror="Server = https://mirrors.ustc.edu.cn/archlinux/\$repo/os/\$arch"

    # 备份原文件
    sudo cp "$mirror_file" "${mirror_file}.backup"
    
    sudo sed -i '/mirrors/d' "$mirror_file"
    # 在文件开头添加 USTC 镜像
    echo -e "${ustc_mirror}\n$(cat ${mirror_file})" | sudo tee "$mirror_file" > /dev/null
    
    # 更新 pacman 数据库（强制更新因为更换了镜像源）
    sudo pacman -Syy
}

# 检查并安装 pacman 依赖
install_pacman_packages() {
    local missing_deps=()
    
    # 检查所有依赖
    for cmd in "${!PACMAN_PKGS[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            local pkg="${PACMAN_PKGS[$cmd]}"
            echo "检测到命令 '$cmd' 未安装 (包名: $pkg)"
            missing_deps+=("$pkg")
        fi
    done
    
    # 如果有缺失的依赖，则安装
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "将安装以下包："
        printf '%s\n' "${missing_deps[@]}"
        sudo pacman -S "${missing_deps[@]}"  # 这里不需要 -y 因为已经在 configure_mirror 中更新过了
    else
        echo "所有 pacman 依赖已安装"
    fi
}

# 安装paru
install_paru() {
    git clone https://aur.archlinux.org/paru.git
    cd paru
    makepkg -si
    cd ..
    rm -rf paru
}

# 使用 paru 安装其他依赖
install_paru_packages() {
    local missing_deps=()
    
    # 检查所有 paru 依赖
    for cmd in "${!PARU_PKGS[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            local pkg="${PARU_PKGS[$cmd]}"
            echo "检测到命令 '$cmd' 未安装 (包名: $pkg)"
            missing_deps+=("$pkg")
        fi
    done
    
    # 如果有缺失的依赖，则安装
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "将通过 paru 安装以下包："
        printf '%s\n' "${missing_deps[@]}"
        echo "是否继续安装？(y/n)"
        read -r install_choice
        if [[ "$install_choice" =~ ^[Yy]$ ]]; then
            for pkg in "${missing_deps[@]}"; do
                proxychains4 paru -S "$pkg"
            done
        else
            echo "用户取消安装"
            exit 1
        fi
    else
        echo "所有 paru 依赖已安装"
    fi
}

# 初始化环境
init_environment() {
    configure_mirror 
    install_pacman_packages
    install_paru
    install_paru_packages
}
# 主程序
main() {
    init_environment
}

main 